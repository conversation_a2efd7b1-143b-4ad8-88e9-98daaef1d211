PACKAGE=mvn clean package -Dmaven.test.skip=true
INSTALL=mvn clean install -Dmaven.test.skip=true

all: pull-src build-commons build-work build-entities index

pull-src:
	git pull

build-commons:
	cd entities/entities-commons && $(INSTALL)

build-entities:
	cd entities && $(PACKAGE)

build-work:
	cd freon-framework && $(PACKAGE)

index:
	cd entities/status-indexer && $(PACKAGE) && mvn dockerfile:build && mvn dockerfile:push