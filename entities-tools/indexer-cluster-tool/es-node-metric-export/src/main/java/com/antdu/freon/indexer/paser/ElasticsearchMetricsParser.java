package com.antdu.freon.indexer.paser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
/**
 * 创建日期 2025/3/11
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */


public class ElasticsearchMetricsParser {
    private final ObjectMapper objectMapper;

    public ElasticsearchMetricsParser() {
        this.objectMapper = new ObjectMapper();
    }

    public JsonNode parse(String json) throws IOException {
        return objectMapper.readTree(json);
    }
}