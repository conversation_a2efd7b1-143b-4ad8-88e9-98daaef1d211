package com.antdu.freon.indexer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import lombok.Data;

/**
 * 创建日期 9/27/22
 * 集群配置时间
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "indexer")
@Data
public class ClusterMapping  {
    /**
     * 配置类
     */
    private Map<String, ClusterNodeConfig> clusterMapping = new HashMap<>();
}
