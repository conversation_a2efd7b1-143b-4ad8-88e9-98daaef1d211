/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.jw.export.core.excel;

import com.github.crab2died.annotation.ExcelField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>根据emoji表情导出twitter数据实体</p>
 * <p/>
 * 创建日期 2020/12/1
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExcelEmojiStatus {

    /**
     * 发帖id
     */
    @ExcelField(title = "id", order = 1)
    private String id;

    /**
     * 对应emoji-keyword主题
     */
    @ExcelField(title = "topicId", order = 2)
    private String topicId;

    /**
     * 发帖人
     */
    @ExcelField(title = "userId", order = 3)
    private String userId;

    /**
     * 发帖时间
     */
    @ExcelField(title = "createAt", order = 4)
    private String createAt;

    /**
     * 发帖类型
     */
    @ExcelField(title = "type", order = 5)
    private String type;

    /**
     * 发帖内容
     */
    @ExcelField(title = "content", order = 6)
    private String content;

    /**
     * 原贴内容
     */
    @ExcelField(title = "originalContent", order = 7)
    private String originalContent;

}
