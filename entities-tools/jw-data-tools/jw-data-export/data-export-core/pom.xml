<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
  ~ 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jw-data-export</artifactId>
        <groupId>com.antdu.freon</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>Antdu.com :: Freon JW Export Data Core</name>
    <artifactId>data-export-core</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.antdu.freon.entities.commons</groupId>
            <artifactId>commons</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.crab2died</groupId>
            <artifactId>Excel4J</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon.entities.commons</groupId>
            <artifactId>disruptor-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.antvision.eagleattack.api</groupId>
            <artifactId>weibo-common</artifactId>
            <version>1.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty</artifactId>
                    <groupId>org.mortbay.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-util</artifactId>
                    <groupId>org.mortbay.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsp-api</artifactId>
                    <groupId>javax.servlet.jsp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon</groupId>
            <artifactId>freon-buka</artifactId>
            <version>${freon.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.12.1</version>
        </dependency>

        <dependency>
            <groupId>com.antdu.microservice</groupId>
            <artifactId>chassis-ratelimit-spring-boot-starter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon.entities.commons</groupId>
            <artifactId>cbase-driver-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>chassis-ratelimit-spring-boot-starter</artifactId>
                    <groupId>com.antdu.microservice</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.antdu.microservice</groupId>
            <artifactId>metrics-reporter-spring-boot-starter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon</groupId>
            <artifactId>freon-caster</artifactId>
            <version>${freon.framework.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon</groupId>
            <artifactId>feign-client</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>


</project>
