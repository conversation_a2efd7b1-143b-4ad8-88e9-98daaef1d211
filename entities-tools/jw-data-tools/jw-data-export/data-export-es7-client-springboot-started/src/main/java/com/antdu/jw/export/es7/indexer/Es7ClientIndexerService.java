/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.jw.export.es7.indexer;

import com.antdu.analysis.pre.AnalysisUtil;
import com.antdu.analysis.pre.AnalyzerConfig;
import com.antdu.jw.export.core.config.MultipleEsClientAutoConfiguration;
import com.antdu.jw.export.core.indexer.IndexerService;
import com.antdu.jw.export.core.utils.IndexerUtil;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.apache.lucene.analysis.Analyzer;
import org.elasticsearch.client.Node;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;

/**
 * <p>es7 索引客户端 查询服务实现</p>
 * <p/>
 * 创建日期 2020/12/3
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
@Component
public class Es7ClientIndexerService extends IndexerService {

    @Override
    public SearchIterable queryStatusIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, StringUtils.EMPTY, StringUtils.EMPTY);
        return getClientResIter(statusClient, queryBuilder, statusIndies, StringUtils.EMPTY);
    }

    @Override
    public Iterable<Set<String>> queryOriginalStatusIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, "0", StringUtils.EMPTY);
        return getClientResIter(statusClient, queryBuilder, statusIndies, StringUtils.EMPTY);
    }

    @Override
    public SearchIterable queryDocumentIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, StringUtils.EMPTY, platform);
        return getClientResIter(documentClient, queryBuilder, documentIndies, StringUtils.EMPTY);
    }

    @Override
    public Iterable<Set<String>> queryOriginalDocumentIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, "0", platform);
        return getClientResIter(documentClient, queryBuilder, documentIndies, StringUtils.EMPTY);
    }

    @Override
    public Iterable<Set<String>> queryWemediaIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, StringUtils.EMPTY, StringUtils.EMPTY);
        return getClientResIter(wemediaClient, queryBuilder, wemediaIndies, StringUtils.EMPTY);
    }

    @Override
    public SearchIterable queryStatusIdIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, StringUtils.EMPTY);
        return getClientResIter(statusClient, getQuery(queryStr), statusIndies, timeRange);
    }

    @Override
    public SearchDocIterable queryStatusUserInfoIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, StringUtils.EMPTY);
        return getSearchDocIterable(statusClient, getQuery(queryStr), statusIndies, timeRange);
    }

    @Override
    public SearchIterable queryDocumentIdIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, platform);
        return getClientResIter(documentClient, getQuery(queryStr), documentIndies, timeRange);
    }

    @Override
    public SearchDocIterable queryDocumentUserInfoIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, platform);
        return getSearchDocIterable(documentClient, getQuery(queryStr), documentIndies, timeRange);
    }

    @Override
    public SearchDocIterable queryWemediaUserInfoIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, StringUtils.EMPTY);
        return getSearchDocIterable(wemediaClient, getQuery(queryStr), wemediaIndies, timeRange);
    }

    /**
     * 博文客户端
     */
    private RestHighLevelClient statusClient;

    /**
     * 网页
     */
    private RestHighLevelClient documentClient;

    /**
     * 自媒体
     */
    private RestHighLevelClient wemediaClient;

    /**
     * 博文 查询索引名
     */
    private String[] statusIndies;

    /**
     * 网页 查询索引名
     */
    private String[] documentIndies;

    /**
     * 自媒体 查询索引名
     */
    private String[] wemediaIndies;

    /**
     * 创建时间字段，微博创建时间不带d
     */
    @Value("${export.createTimeField:createdAt}")
    private String createTimeField;

    /**
     * 分词器
     */
    private Analyzer analyzer;
    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        MultipleEsClientAutoConfiguration.ClusterInfo statusClientInfo =
            configuration.getMap().get(STATUS_ES_CLIENT);
        MultipleEsClientAutoConfiguration.ClusterInfo documentClientInfo =
            configuration.getMap().get(DOCUMENT_ES_CLIENT);
        MultipleEsClientAutoConfiguration.ClusterInfo wemediaClientInfo =
            configuration.getMap().get(WEMEDIA_ES_CLIENT);

        this.statusIndies = statusIndiesStr.split(",");
        this.documentIndies = documentIndiesStr.split(",");
        this.wemediaIndies = wemediaIndiesStr.split(",");

        statusClient = newRestClient(statusClientInfo.getHosts(), statusClientInfo.getPort(),
                                     statusClientInfo.getConnectionTime());
        documentClient = newRestClient(documentClientInfo.getHosts(), documentClientInfo.getPort(),
                                       documentClientInfo.getConnectionTime());
        wemediaClient = newRestClient(wemediaClientInfo.getHosts(), wemediaClientInfo.getPort(),
                                      wemediaClientInfo.getConnectionTime());

        analyzer = new AnalyzerConfig().jiebaAnalyzer();
    }


    /**
     * 公共查询方法
     *
     * @param client    client
     * @param query     query
     * @param indies    indies
     * @param timeRange 时间范围
     * @return Set
     * @throws IOException ioException
     */
    private SearchIterable getClientResIter(RestHighLevelClient client, QueryBuilder query,
                                            String[] indies, String timeRange) {
        return new SearchIterable(client, query, indies, batchSize, timeRange);
    }

    /**
     * 公共查询方法
     *
     * @param client    client
     * @param query     query
     * @param indies    indies
     * @param timeRange 时间范围
     * @return Set
     * @throws IOException ioException
     */
    private SearchDocIterable getSearchDocIterable(RestHighLevelClient client, QueryBuilder query,
                                                   String[] indies, String timeRange) {
        return new SearchDocIterable(client, query, indies, batchSize, timeRange);
    }

    /**
     * 根据查询参数构建查询器
     *
     * @param queryStr 查询参数
     * @return 查询构建器
     */
    private QueryBuilder getQuery(String queryStr) {
        //分词前置处理
        return AnalysisUtil.analysisPre(queryStr, "entireContent", analyzer); //原贴+现帖内容
    }

    /**
     * 构建一个bool es查询器查询
     *
     * @param userId    用户id
     * @param stateType 博文类型
     * @param platform  平台
     * @return 查询器
     */
    private BoolQueryBuilder getBoolQueryBuilder(String userId, String stateType, String platform) {
        long now = System.currentTimeMillis();
        BoolQueryBuilder queryBuilder = boolQuery();
        if (StringUtils.isNotBlank(userId)) {
            queryBuilder.must(termQuery("userId", userId));
        }
        if (StringUtils.isNotBlank(stateType)) {
            queryBuilder.must(termQuery("stateType", Integer.parseInt(stateType)));
        }
        if (StringUtils.isNotBlank(platform)) {
            queryBuilder.must(termQuery("platform", platform));
        }
        queryBuilder.filter(rangeQuery(createTimeField).from(now - timeDifference).to(now));
        return queryBuilder;
    }

    /**
     * es 客户端
     *
     * @param clusterNodes   客户端节点
     * @param port           客户端端口
     * @param connectionTime 客户端连接超时时间
     * @return
     */
    public RestHighLevelClient newRestClient(String clusterNodes, int port, int connectionTime) {
        String[] nodeIps = clusterNodes.split(",");
        List<HttpHost> httpHostList = new ArrayList<>();
        for (String nodeIp : nodeIps) {
            httpHostList.add(new HttpHost(nodeIp, port));
        }

        RestClientBuilder clientBuilder =
            RestClient.builder(httpHostList.toArray(new HttpHost[]{}))
                      .setFailureListener(new RestClient.FailureListener() {
                          @Override
                          public void onFailure(Node host) {
                              super.onFailure(host);
                              log.error("FAILURE !!!! FailureListener HAS WOKEN UP!!!!: " + host.toString());
                          }
                      }).setRequestConfigCallback(requestConfigBuilder ->
                                                      requestConfigBuilder.setConnectTimeout(connectionTime)
                                                                          .setSocketTimeout(connectionTime)
                                                                          .setConnectionRequestTimeout(0));
        return new RestHighLevelClient(clientBuilder);
    }

    /**
     * 配置   网页 查询索引名 逗号分隔
     */
    @Value("${es.wemediaIndiesStr:media_90}")
    protected String wemediaIndiesStr;

    /**
     * 自媒体索引客户端str
     */
    private static final String WEMEDIA_ES_CLIENT = "wemedia_es_client";

    @Override
    public long queryCountStatusIdByKeyword(String keyword, String timeRange) {
        return countResponse(keyword, timeRange, statusIndies, statusClient);
    }

    @Override
    public long queryCountDocumentIdByKeyword(String keyword, String timeRange) {
        return countResponse(keyword, timeRange, documentIndies, documentClient);
    }

    @Override
    public long queryCountWemediaIdByKeyword(String keyword, String timeRange) {
        return countResponse(keyword, timeRange, wemediaIndies, wemediaClient);
    }

    /**
     * 统计count
     *
     * @param keyword   关键词
     * @param timeRange 时间范围
     * @param indies    indies
     * @param client    客户端
     * @return count
     */
    private long countResponse(String keyword, String timeRange, String[] indies, RestHighLevelClient client) {
        if (StringUtils.isBlank(timeRange) || "null".equalsIgnoreCase(timeRange)) {
            return maxCount;
        }
        CountRequest request = new CountRequest();
        request.indices(IndexerUtil.calcIndexName(timeRange, indies));
        String queryStr = buildQueryStr(keyword, timeRange, platform);
        request.source(new SearchSourceBuilder().query(getQuery(queryStr)));
        try {
            return client.count(request, RequestOptions.DEFAULT).getCount();
        } catch (IOException e) {
            log.error("Query count error", e);
            return 0;
        }
    }

}
