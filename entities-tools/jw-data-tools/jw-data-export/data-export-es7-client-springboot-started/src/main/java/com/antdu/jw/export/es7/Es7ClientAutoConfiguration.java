/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.jw.export.es7;

import com.antdu.jw.export.es7.indexer.Es7ClientIndexerService;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <p>es7 客户端自动化配置类</p>
 * <p/>
 * 创建日期 2020/12/3
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Configuration
@Import({Es7ClientIndexerService.class})
public class Es7ClientAutoConfiguration {
}
