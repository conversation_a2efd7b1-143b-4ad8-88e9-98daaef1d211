/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.jw.export.es6.indexer;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.queryStringQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;

import com.antdu.jw.export.core.config.MultipleEsClientAutoConfiguration;
import com.antdu.jw.export.core.indexer.DocUserInfo;
import com.antdu.jw.export.core.indexer.IndexerService;
import com.antdu.jw.export.core.utils.IndexerUtil;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>es6 索引客户端 查询服务实现</p>
 * <p/>
 * 创建日期 2020/12/3
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
@Component
@SuppressWarnings({"checkstyle:MagicNumber"})
public class Es6ClientIndexerService extends IndexerService {

    @Override
    public SearchIterable queryStatusIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, false, StringUtils.EMPTY);
        return getClientResIter(statusClient, queryBuilder, statusIndies, StringUtils.EMPTY);
    }

    @Override
    public Iterable<Set<String>> queryOriginalStatusIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, true, StringUtils.EMPTY);
        return getClientResIter(statusClient, queryBuilder, statusIndies, StringUtils.EMPTY);
    }

    @Override
    public SearchIterable queryDocumentIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, false, platform);
        return getClientResIter(documentClient, queryBuilder, documentIndies, StringUtils.EMPTY);
    }

    @Override
    public Iterable<Set<String>> queryOriginalDocumentIdIterByUserId(String userId) {
        BoolQueryBuilder queryBuilder = getBoolQueryBuilder(userId, true, platform);
        return getClientResIter(documentClient, queryBuilder, documentIndies, StringUtils.EMPTY);
    }

    @Override
    public Iterable<Set<String>> queryWemediaIdIterByUserId(String userId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public SearchIterable queryStatusIdIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, StringUtils.EMPTY);
        return getClientResIter(statusClient, getQuery(queryStr), statusIndies, timeRange);
    }

    @Override
    public SearchDocIterable queryStatusUserInfoIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, StringUtils.EMPTY);
        return getSearchDocIterable(statusClient, getQuery(queryStr), statusIndies, timeRange);
    }

    @Override
    public SearchIterable queryDocumentIdIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, platform);
        return getClientResIter(documentClient, getQuery(queryStr), documentIndies, timeRange);
    }

    @Override
    public SearchDocIterable queryDocumentUserInfoIterByKeyword(String keyword, String timeRange) {
        String queryStr = buildQueryStr(keyword, timeRange, platform);
        return getSearchDocIterable(documentClient, getQuery(queryStr), documentIndies, timeRange);
    }

    @Override
    public Iterable<Set<DocUserInfo>> queryWemediaUserInfoIterByKeyword(String keyword, String timeRange) {
        throw new UnsupportedOperationException();
    }

    /**
     * 博文客户端
     */
    private RestHighLevelClient statusClient;

    /**
     * 网页
     */
    private RestHighLevelClient documentClient;

    /**
     * 博文 查询索引名
     */
    private String[] statusIndies;

    /**
     * 网页 查询索引名
     */
    private String[] documentIndies;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        MultipleEsClientAutoConfiguration.ClusterInfo statusClientInfo =
            configuration.getMap().get(STATUS_ES_CLIENT);
        MultipleEsClientAutoConfiguration.ClusterInfo documentClientInfo =
            configuration.getMap().get(DOCUMENT_ES_CLIENT);
        this.statusIndies = statusIndiesStr.split(",");
        this.documentIndies = documentIndiesStr.split(",");
        statusClient = newRestClient(statusClientInfo.getHosts(), statusClientInfo.getPort(),
                                     statusClientInfo.getConnectionTime());
        documentClient = newRestClient(documentClientInfo.getHosts(), documentClientInfo.getPort(),
                                       documentClientInfo.getConnectionTime());
    }

    /**
     * es 客户端
     *
     * @param clusterNodes   客户端节点
     * @param port           客户端端口
     * @param connectionTime 客户端连接超时时间
     * @return
     */
    public RestHighLevelClient newRestClient(String clusterNodes, int port, int connectionTime) {
        String[] nodeIps = clusterNodes.split(",");
        List<HttpHost> httpHostList = new ArrayList<>();
        for (String nodeIp : nodeIps) {
            httpHostList.add(new HttpHost(nodeIp, port));
        }

        RestClientBuilder clientBuilder =
            RestClient.builder(httpHostList.toArray(new HttpHost[]{}))
                      .setFailureListener(new RestClient.FailureListener() {
                          @Override
                          public void onFailure(HttpHost host) {
                              super.onFailure(host);
                              log.error("FAILURE !!!! FailureListener HAS WOKEN UP!!!!: " + host.toString());
                          }
                      }).setRequestConfigCallback(requestConfigBuilder ->
                                                      requestConfigBuilder.setConnectTimeout(connectionTime)
                                                                          .setSocketTimeout(connectionTime)
                                                                          .setConnectionRequestTimeout(0));
        return new RestHighLevelClient(clientBuilder);
    }


    /**
     * 公共查询方法
     *
     * @param client    client
     * @param query     query
     * @param indies    indies
     * @param timeRange 时间范围
     * @return Set
     * @throws IOException ioException
     */
    private SearchIterable getClientResIter(RestHighLevelClient client, QueryBuilder query,
                                            String[] indies, String timeRange) {
        return new SearchIterable(client, query, indies, batchSize, timeRange);
    }

    /**
     * 公共查询方法
     *
     * @param client    client
     * @param query     query
     * @param indies    indies
     * @param timeRange 时间范围
     * @return Set
     * @throws IOException ioException
     */
    private SearchDocIterable getSearchDocIterable(RestHighLevelClient client, QueryBuilder query,
                                                   String[] indies, String timeRange) {
        return new SearchDocIterable(client, query, indies, batchSize, timeRange);
    }

    /**
     * 根据查询参数构建查询器
     *
     * @param queryStr 查询参数
     * @return 查询构建器
     */
    private QueryBuilder getQuery(String queryStr) {
        QueryBuilder originalBuilder = queryStringQuery(queryStr)
            .defaultField("originalContent")    //原贴内容
            .defaultOperator(Operator.AND);

        QueryBuilder currentBuilder = queryStringQuery(queryStr)
            .defaultField("currentContent")     //现帖内容
            .defaultOperator(Operator.AND);
        return boolQuery().should(originalBuilder).should(currentBuilder);
    }

    /**
     * 构建一个bool es查询器查询
     *
     * @param userId     用户id
     * @param isOriginal 是否筛选原贴
     * @param platform   平台
     * @return 查询器
     */
    private BoolQueryBuilder getBoolQueryBuilder(String userId, boolean isOriginal, String platform) {
        long now = System.currentTimeMillis();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(userId)) {
            queryBuilder.must(termQuery("userId", userId));
        }
        if (isOriginal) {   //筛选原贴
            queryBuilder.must(termQuery("retweetId", -1));
            queryBuilder.must(termQuery("replyStatusId", -1));
        }
        if (StringUtils.isNotBlank(platform)) {
            queryBuilder.must(termQuery("platform", platform));
        }
        queryBuilder.filter(rangeQuery("createdAt").from(now - timeDifference).to(now));
        return queryBuilder;
    }

    @Override
    public long queryCountStatusIdByKeyword(String keyword, String timeRange) {
        return countResponse(keyword, timeRange, statusIndies, statusClient);
    }

    @Override
    public long queryCountDocumentIdByKeyword(String keyword, String timeRange) {
        return countResponse(keyword, timeRange, documentIndies, documentClient);
    }

    @Override
    public long queryCountWemediaIdByKeyword(String keyword, String timeRange) {
        throw new UnsupportedOperationException();
    }

    /**
     * 统计count
     *
     * @param keyword   关键词
     * @param timeRange 时间范围
     * @param indies    indies
     * @param client    客户端
     * @return count
     */
    private long countResponse(String keyword, String timeRange, String[] indies, RestHighLevelClient client) {

        if (StringUtils.isBlank(timeRange) || "null".equalsIgnoreCase(timeRange)) {
            return maxCount;
        }

        String queryStr = buildQueryStr(keyword, timeRange, platform);

        String[] indexName = IndexerUtil.calcIndexName(timeRange, indies);

        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(getQuery(queryStr));
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse response = client.search(searchRequest);
            return response.getHits().totalHits;
        } catch (IOException e) {
            log.error("Query count error", e);
            return 0;
        }
    }


}
