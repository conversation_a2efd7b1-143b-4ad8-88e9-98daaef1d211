/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.jw.yu.export.cbase;

import com.antdu.freon.cbase.driverclient.core.CqlSessionWrapper;
import com.antdu.freon.cbase.driverclient.utils.ResultSet2ProtoMessageUtils;
import com.antdu.jw.export.core.cbase.CbaseRowSearcher;
import com.datastax.oss.driver.api.core.CqlSession;
import com.datastax.oss.driver.api.core.cql.Row;
import com.google.common.collect.Maps;
import com.google.protobuf.Descriptors;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>skinny row 查询器</p>
 * <p/>
 * 创建日期 2020/12/5
 *
 * @param <T> 实体类型
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
public class SkinnyRowSearcher<T extends Message> extends CbaseRowSearcher {

    /**
     * 根据cql查询
     *
     * @param cluster           查询的库
     * @param getExecuteCqlFunc 获取执行cql语句方法
     * @param keys              查询主键集合
     * @return 查询结果集
     */
    protected Map<String, Row> getAll(String cluster, Function<String, String> getExecuteCqlFunc, Set<String> keys) {
        acquire(keys.size());

        Map<String, Row> resultMap = Maps.newHashMap();

        CqlSessionWrapper wrapper = sessionPoolMap.get(cluster);

        final CountDownLatch latch = new CountDownLatch(keys.size());
        CqlSession session = wrapper.getSession();

        keys.forEach(key -> session.executeAsync(
            getBoundStatement(session, getExecuteCqlFunc.apply(key), wrapper.getReaderConsistencyLevel(), key))
                                   .whenCompleteAsync(skinnyRowGetAllAction(key, resultMap, latch),
                                                      executorServicePool.buildExecutorService(POOL_NAME)));

        try {
            if (!latch.await(latchWait, TimeUnit.MILLISECONDS)) {
                log.warn("countdownlatch await timeout,param:{}", StringUtils.join(keys, ","));
            }
        } catch (InterruptedException e) {
            log.error("latch wait InterruptedException", e);
        }

        if (resultMap.size() != keys.size() && log.isDebugEnabled()) {
            log.debug("resultListMap.size:{},keys.size:{}", resultMap.size(), keys.size());
        }
        return resultMap;
    }

    /**
     * 批量查询
     *
     * @param keys              查询主键集合
     * @param cluster           集群信息
     * @param getExecuteCqlFunc 获取执行cql方法
     * @param builder           message 构建器
     * @param descriptorMap     message字段映射
     * @return 结果集
     */
    @SuppressWarnings("unchecked")
    protected Map<String, T> query(Set<String> keys, String cluster,
                                   Function<String, String> getExecuteCqlFunc,
                                   GeneratedMessageV3.Builder builder,
                                   Map<String, Descriptors.FieldDescriptor> descriptorMap) {
        Map<String, Row> rowMap = getAll(cluster, getExecuteCqlFunc, keys);
        return ResultSet2ProtoMessageUtils.convert(rowMap, builder, descriptorMap);
    }

}
