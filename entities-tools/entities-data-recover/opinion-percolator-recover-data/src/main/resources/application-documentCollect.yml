server:
  port: 8864

spring:
  application:
    name: rebuild-opinion-data-application-local

broker:
  topic:
    name: rebuild-opinion-data-broker
  persistence:
    id: opinion-recover-web-persistence

buka:
  enable:
    run:
      cbase: true
      caster: false
  caster:
    group:
      id: skinny-status-group-caster
  cbase:
    group:
      id: skinny-status-group-cbase
  bootstrap:
    servers: 10.20.2.49:9096,10.20.2.50:9096,10.20.2.51:9096,10.20.2.52:9096,10.20.2.53:9096,10.20.2.54:9096
  client:
    id: cbase-storage-skinny-status-consumer
  auto:
    offset:
      reset: earliest
  consumer:
    topic:  recover-opinion-collection-data
  producer:
    topic:  recover-opinion-collection-data

cbase:
  clusters:
    list:
      - hot_cluster:
          clusterNodes: 10.20.6.9,10.20.6.10,10.20.6.11,10.20.6.12,10.20.6.18,10.20.6.24
          clusterName: opinionCbaseHot
          port: 9043
          username: cassandra
          password: cassandra
          operateTimeout: 30000
          ttlMaxDays: 7
          ttlMax: 604800
          ttlMin: 86400
  keyspace: yuqing_collect
  table: opinion_sorted_set_desc
  table_keyname: docid
  complexfiled:  #复杂字段
    obj:
    collect:

readCache:
  maxSize: 100000

writeCache:
  ttl:
    seconds: 1800

antdu:
  discovery:
    serverUrl:  http://metaserver.dev-antducloud.com/
  metrics:
    clusterName:  opinion-recover-web-persistence
    url:  http://metrics.dev-antducloud.com/metric/report
    slf4jEnable: false
    jmxEnable:  false
    httpEnable: false
    slf4jTimeInterval: 60
    httpTimeInterval: 60

eureka:
  instance:
    preferIpAddress: false
  client:
    enable: false
    serviceUrl:
      defaultZone: http://coordinator.dev-antducloud.com/eureka/v2/


cache:
  caster:
    cluster:
      name: redis-yuqing-test     #caster集群名称

caster:
  serviceName: caster-yuqing-query  #caster服务名称
  codec: lz4                        # value 序列化类型，protobuf 或 lz4，默认 lz4
  clientRetryInterval: 5000         #caster客户端重试时间间隔(毫秒)，默认值5000
  timeout: 10000                    #处理超时时间(毫秒)，默认值10000
  messageMaxBytes: 10485760
  dcoveryAddress: http://metaserver.dev-antducloud.com/

ratelimit:
  client:
    backpress:
      maxsize: 2000
    retry:
      interval: 5000
  service_id: Antdu-Ratelimiter-Service

max:
  request:
    time_second:  1
    capacity: 10000

persistence:
  rate_limit:
    qps: 200000
    caster:
      qps: 500
    cbase:
      qps: 500
    grapher:
      qps: 100

jetty:
  threadPool:
    idleTimeout: 60000
    maxThreads: 10240
    minThreads: 10

batch:
  query:
    maxSize: 100

persist:
  executor:
    max_thread: 1

log:
  print:
    interval: 5000

topic:
  jsonfile:
    dir: /Users/<USER>/Downloads/topic_jsons/eagtek

rebuild:
  begin: 2020-02-15
  end:  2020-02-17
  platform: twitter
  min_stamp: 1581955200000 #若数据createtime小于这个时间戳，则过滤 2-18~2-24

percolator:
  topic:
    interval: 10
    current:
      interval: 0
  write:
    cbase: true