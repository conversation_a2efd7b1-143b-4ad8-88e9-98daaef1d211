<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
  ~ 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
  -->

<configuration status="WARN" monitorInterval="30">

    <properties>
        <property name="style">[%d{HH:mm:ss}] %p [%c{1.}] [%t] [%l] [%r] - %m%n</property>
    </properties>

    <appenders>
        <Console name="console" target="SYSTEM_OUT" ignoreExceptions="false">
            <PatternLayout pattern="${style}"/>
        </Console>

        <RollingRandomAccessFile name="baseFile" fileName="${sys:log_home}/${sys:app_name}.log"
                                 filePattern="${sys:log_home}/${sys:app_name}-%d{yyyy-MM-dd}.log.gz">
            <PatternLayout>
                <pattern>${style}</pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${sys:log_home}/" maxDepth="1">
                    <IfFileName glob="${sys:app_name}-*.log.gz"/>
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <Async name="file">
            <AppenderRef ref="baseFile"/>
        </Async>

    </appenders>

    <loggers>
        <root level="${sys:log_level:-INFO}" includeLocation="true">
            <AppenderRef ref="${sys:append_ref:-console}"/>
        </root>
    </loggers>
</configuration>