package com.antdu.freon.tobuka;

import com.antfact.commons.v2.model.StatusPack;

import com.antdu.nest.highlight.utils.HighLightUtils;
import com.google.gson.Gson;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2022/4/24
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
public class HarmMain {
    static final Map<String, String> MAP = new HashMap<>();
    static final Map<String, Class> CLASS_MAP = new HashMap<>();
    static final String TAGS_URL = "http://freon.dev-antducloud.com/tags/batch/query";
    static final String PURE_TAGS_URL = "http://freon.dev-antducloud.com/pure-text-tags/batch/query";
    static final String PROPS_URL = "http://freon.dev-antducloud.com/mutaprop/batch/query";

    static {
        MAP.put("status", "http://freon.dev-antducloud.com/status/batch/query");
        MAP.put("document", "http://freon.dev-antducloud.com/document/batch/query");
        MAP.put("wemedia", "http://freon.dev-antducloud.com/wemedia/batch/query");
        MAP.put("statusId", "statusId");
        MAP.put("documentId", "docId");
        MAP.put("wemediaId", "wmId");
        CLASS_MAP.put("status", StatusPack.Status.class);
        CLASS_MAP.put("document", StatusPack.Document.class);
        CLASS_MAP.put("wemedia", StatusPack.WeMedia.class);
        MAP.put("statusTopic", "tweet_similar_tag"); //tweet_similar_tag
        MAP.put("documentTopic", "webpage_similar_tag"); // webpage_similar_tag webpage_privacy_tagged
        MAP.put("wemediaTopic", "wemedia_similarity_checked"); //wemedia_privacy_tagged  ,wemedia_similarity_checked
    }

    public static void main(String[] args) throws Exception {
        RestTemplate restTemplate = new RestTemplate();

//        String strIds = String.join(",", docIds);

        MultiValueMap<String, Object> multiMap = new LinkedMultiValueMap<>();
        multiMap.add("ids", "0D44DD1605625553DDE20A44824F9056");

        // 增加请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(multiMap, headers);

        ParameterizedTypeReference<List<Map<String, Object>>> LIST_TYPE = new ParameterizedTypeReference<List<Map<String, Object>>>() {
        };
        ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange("http://cbase.antfact.com/cbase/query/webtable_harmful/content/bulk", HttpMethod.POST,
                                                                                   entity, LIST_TYPE, (Object) null);
        MultiValueMap<String, String> objectObjectHashMap = new LinkedMultiValueMap<>();
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("ids", "0D44DD1605625553DDE20A44824F9056");
        HttpEntity httpEntity = new HttpEntity(objectObjectHashMap, headers);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(objectObjectHashMap, headers);
//        objectObjectHashMap.put("sortClause", );
        long l = System.currentTimeMillis();
        List<Map<Integer, Integer>> resultArray = new ArrayList<>();
        int count = 0;
        boolean flag = false;
        List<Map<String, Object>> body = response.getBody();
        String content = (String) body.get(0).get("content");
        resultArray.add(new HashMap<Integer, Integer>(){{put(1,11);}});
        System.out.println(HighLightUtils.highlight(content, resultArray));
        System.out.println(System.currentTimeMillis() - l);
        System.out.println(count);
    }
}
