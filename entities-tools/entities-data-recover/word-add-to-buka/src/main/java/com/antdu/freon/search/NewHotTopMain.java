package com.antdu.freon.search;

//import cn.antvision.eagleattack.nest.analyzer.lucene.BinaryAnalyzer;
//
//import com.google.gson.Gson;
//
//import org.apache.http.HttpHost;
//import org.elasticsearch.action.search.ClearScrollRequest;
//import org.elasticsearch.action.search.SearchRequest;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.action.search.SearchScrollRequest;
//import org.elasticsearch.client.Node;
//import org.elasticsearch.client.RequestOptions;
//import org.elasticsearch.client.RestClient;
//import org.elasticsearch.client.RestClientBuilder;
//import org.elasticsearch.client.RestHighLevelClient;
//import org.elasticsearch.common.unit.TimeValue;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.search.Scroll;
//import org.elasticsearch.search.SearchHit;
//import org.elasticsearch.search.builder.SearchSourceBuilder;
//import org.jsoup.helper.StringUtil;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.client.RestTemplate;
//
//import java.io.BufferedWriter;
//import java.io.FileWriter;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Map;
//
//import lombok.extern.slf4j.Slf4j;

import com.antfact.commons.v2.ProtoBufFormatter;
import com.antfact.commons.v2.model.StatusPack;
import com.antfact.commons.v2.model.image.ImagePack;

import com.antdu.freon.commons.entity.FreonCommons;

import org.apache.http.HttpHost;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.elasticsearch.client.RestClient;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.util.Collections;
import java.util.Properties;


/**
 * 创建日期 2022/4/24
 * 数据从索引导出，存放到本地文件
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
//@Slf4j
public class NewHotTopMain {
//    public static void main(String... args) throws Exception {
//        Properties properties = new Properties();
//
//        //buka相关配置
//        properties.put(FreonCommons.Buka.BOOTSTARP_SERVERS, "**********:9096");
//        properties.put(FreonCommons.Buka.KEY_DESERIALIZER, ByteArrayDeserializer.class);
//        properties.put(FreonCommons.Buka.VALUE_DESERIALIZER, ByteArrayDeserializer.class);
//        properties.put(FreonCommons.Buka.GROUP_ID, "test1");
//        properties.put(FreonCommons.Buka.ENABLE_AUTO_COMMIT_CONFIG, "true");
//        properties.put(FreonCommons.Buka.AUTO_OFFSET_RESET, "latest");
//        KafkaConsumer<byte[], byte[]> consumer = new KafkaConsumer<byte[], byte[]>(properties);
//        consumer.subscribe(Collections.singleton("image_feature_extraction_result"));
////        BufferedWriter writer =  new BufferedWriter(new FileWriter("/Users/<USER>/test-topic"));
////        consumer.subscribe(Collections.singleton("wemedia_similarity_checked"));
//        Properties properties1 = new Properties();
//
//        RestClient client = RestClient.builder(new HttpHost("***********", 9200)).build();
//        ElasticsearchTransport transport = new RestClientTransport(
//            client, new JacksonJsonpMapper());
//        ElasticsearchClient elasticsearchClient = new ElasticsearchClient(transport);
////        elasticsearchClient.indices().create(builder -> builder.index("image_feature_extraction_result"));
//        //buka相关配置
//        while (true) {
//            ConsumerRecords<byte[], byte[]> poll = consumer.poll(100);
//            if (poll == null) {
//                continue;
//            }
//            boolean flag = false;
//            for (ConsumerRecord<byte[], byte[]> consumerRecord : poll) {
//
//                byte[] value = consumerRecord.value();
//                ImagePack.ImageDocument document = ProtoBufFormatter.fromByteArray(ImagePack.ImageDocument.class, value);
//                if (System.currentTimeMillis() - document.getCreatedAt()  > 604800000L) {
//                    continue;
//                }
//                if (document.getDocId().length() == 0 || document.getTags().getTextSemanticVectorList().isEmpty()) {
//                    continue;
//                }
//
//                Float[] objects = document.getTags().getTextSemanticVectorList().toArray(new Float[0]);
////                if (objects  == null || objects.length == 0) {
////                    continue;
////                }
//                ImageVector imageVector = new ImageVector();
//                imageVector.setVector(objects);
//                try {
//
//                    elasticsearchClient.index(i -> i.index("image_feature_extraction_result").id(document.getDocId()).document(imageVector));
//                } catch (Exception e) {
//                    System.out.println(e);
//                }
//
//            }
//
//        }
//    }
}
