package com.antdu.freon.recover.file.test;

import com.antfact.commons.v2.MessageSetUtils;
import com.antfact.commons.v2.ProtoBufFormatter;
import com.antfact.commons.v2.model.StatusPack;

import com.alibaba.fastjson.JSONObject;
import com.antdu.analysis.pre.AnalysisUtil;
import com.antdu.analysis.pre.AnalyzerConfig;
import com.antdu.freon.commons.entity.FreonCommons;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.hadoop.yarn.webapp.hamlet2.Hamlet;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.lucene.analysis.Analyzer;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.Node;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.QueryStringQueryBuilder;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jsoup.helper.StringUtil;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2021/9/8
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
public class CsvTest {
    private static final byte[] UTF_8_DOM = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};

    private RestHighLevelClient restHighLevelClient;
    @Before
    public void  init() {
        RestClientBuilder builder = RestClient.builder(Arrays.stream("***********,***********".split(","))
                                                                                       .map(a -> new HttpHost(a, 9600))
                                                                                       .toArray(HttpHost[]::new))
                                                                        .setRequestConfigCallback(requestConfigBuilder -> {
                                                                            requestConfigBuilder.setConnectTimeout(60000);
                                                                            requestConfigBuilder.setSocketTimeout(60000);
                                                                            requestConfigBuilder.setConnectionRequestTimeout(20000);
                                                                            return requestConfigBuilder;
                                                                        });
        builder.setHttpClientConfigCallback(httpAsyncClientBuilder -> {
            httpAsyncClientBuilder.disableAuthCaching();
            BasicCredentialsProvider basicCredentialsProvider =
                new BasicCredentialsProvider();
            basicCredentialsProvider.setCredentials(
                AuthScope.ANY,
                new UsernamePasswordCredentials("elastic", "123456"));
            return httpAsyncClientBuilder
                .setDefaultCredentialsProvider(basicCredentialsProvider);
        });
        restHighLevelClient = new RestHighLevelClient(builder);
    }
    @Test
    public void bloggerRestore() throws IOException {

        SearchRequest searchRequest = new SearchRequest("blogger_graph");
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        boolQueryBuilder.should(QueryBuilders.termQuery("language", "tw"));
        boolQueryBuilder.must(QueryBuilders.termQuery("platform", "twitter"));
        searchSourceBuilder.query(boolQueryBuilder).size(10000);
        searchSourceBuilder.sort("followersCount", SortOrder.DESC);
        searchSourceBuilder.sort("favouritesCount", SortOrder.DESC);
        searchRequest.source(searchSourceBuilder);
        BufferedWriter writer = new BufferedWriter(new FileWriter(""));
        OutputStreamWriter
            outputStreamWriter = new OutputStreamWriter(new FileOutputStream("" ), StandardCharsets.UTF_8);
        outputStreamWriter.append(new String(UTF_8_DOM, StandardCharsets.UTF_8));
        CSVPrinter printer = new CSVPrinter(outputStreamWriter, CSVFormat.EXCEL);
        printer.print("docId");
        printer.print("userName");
        printer.print("nickName");
        printer.print("location");
        printer.print("description");
        printer.print("createAt");
        printer.print("vip");
        printer.print("vipType");
        printer.print("vipInfo");
        printer.print("profileImageUrl");
        printer.print("followersCount");
        printer.print("friendsCount");
        printer.print("favouritesCount");
        printer.print("statusesCount");
        printer.println();
        SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        for (SearchHit hit : response.getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            printer.print(sourceAsMap.get("bloggerId").toString());
            writer.write("userId:" + sourceAsMap.get("bloggerId").toString().substring(2));
            writer.newLine();
            printer.print(sourceAsMap.get("bloggerName").toString());
            printer.print(sourceAsMap.get("nickName").toString());
            printer.print(sourceAsMap.get("location") == null ? "" : sourceAsMap.get("location").toString());
            printer.print(sourceAsMap.get("description")== null ? "" : sourceAsMap.get("description").toString().replace("\n"," "));
            printer.print(sourceAsMap.get("createdAt")== null ? "" : sourceAsMap.get("createdAt").toString());
            printer.print(sourceAsMap.get("vip")== null ? "" : sourceAsMap.get("vip").toString());
            printer.print(sourceAsMap.get("vipType")== null ? "" : sourceAsMap.get("vipType").toString());
            printer.print(sourceAsMap.get("vipInfo") == null ? "" : sourceAsMap.get("vipInfo").toString());
            printer.print(sourceAsMap.get("profileImageUrl")== null ? "" : sourceAsMap.get("profileImageUrl").toString());
            printer.print(sourceAsMap.get("followersCount").toString());
            printer.print(sourceAsMap.get("friendsCount").toString());
            printer.print(sourceAsMap.get("favouritesCount").toString());
            printer.print(sourceAsMap.get("statusesCount").toString());
            printer.println();
        }
        printer.flush();
        printer.close();
        writer.flush();
        writer.close();
    }
    @Test
    public void bloggerDataRestore() throws IOException {

        SearchRequest searchRequest = new SearchRequest("status*");
        BufferedWriter writer = new BufferedWriter(new FileWriter("",false));
        BufferedReader reader = new BufferedReader(new FileReader(""));
        String str = reader.readLine();
        writer.write(str);
        writer.newLine();
        while ((str = reader.readLine()) != null) {
            writer.write("tw" + str);
            writer.newLine();
        }
        writer.flush();
        writer.close();
    }
    @Test
    public void test() throws IOException {
        int timeout = 60;
        List<HttpHost> httpHostList = new ArrayList<>();
        for (String nodeIp : "**********".split(",")) {
            httpHostList.add(new HttpHost(nodeIp, 9700));
        }
        RestClientBuilder clientBuilder =
            RestClient.builder(httpHostList.toArray(new HttpHost[] {}))
                      .setFailureListener(new RestClient.FailureListener() {
                          @Override
                          public void onFailure(Node node) {
                              log.error("FAILURE !!!! FailureListener HAS WOKEN UP!!!!: "
                                            + node.toString());
                          }
                      })
                      .setRequestConfigCallback(requestConfigBuilder ->
                                                    requestConfigBuilder
                                                        .setConnectTimeout(timeout * 1000)
                                                        .setSocketTimeout(timeout * 1000)
                                                        .setConnectionRequestTimeout(timeout / 10 * 1000));
        Analyzer analyzer = new AnalyzerConfig().jiebaAnalyzer();
        QueryBuilder
            queryBuilder = AnalysisUtil.analysisPre("\"水火无情\"  && mediaType:short_video && wmType:0", "entireContent", analyzer);
        RestHighLevelClient restHighLevelClient = new RestHighLevelClient(clientBuilder);
        SearchRequest searchRequest = new SearchRequest("media");
        searchRequest.scroll(new Scroll(TimeValue.timeValueMinutes(5L)));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(400);
        searchRequest.source(searchSourceBuilder);
        SearchResponse search = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        List<String> list = new ArrayList<>();
        String scrollId = search.getScrollId();
        while (scrollId != null && search.getHits().getHits().length != 0) {
            for (SearchHit hit : search.getHits().getHits()) {
                list.add(hit.getId());
            }
            if (list.size() >= 3000) {
                break;
            }
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(new Scroll(TimeValue.timeValueMinutes(5L)));
            search = restHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
        } ;
        BufferedWriter writer2 = new BufferedWriter(new FileWriter("/Users/<USER>/keyWordImage"));
        for (String s : list) {
            writer2.write(s);
            writer2.newLine();
        }
        writer2.flush();
        writer2.close();
        System.out.println(queryBuilder);
        assert  queryBuilder != null;
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        List<StatusPack.Status> dataList = new ArrayList<>();
        for (List<String> stringList : Lists.partition(list, 100)){
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            if (stringList.size() == 0) {
                break;
            }
            int timeOutCount = 0;
            boolean isSuccess = false;
            map.add("ids", StringUtil.join(stringList.iterator(), ","));
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
            while (!isSuccess) {
                try {
                    ResponseEntity<byte[]> responseEntity = restTemplate.postForEntity("http://freon.dev-antducloud.com/document/batch/query", request, byte[].class);
                    if (responseEntity.getBody() != null && responseEntity.getBody().length != 0) {
                        dataList.addAll(MessageSetUtils.fromByteArray(StatusPack.Status.class, responseEntity.getBody()));
                    }
                    isSuccess = true;
                } catch (Exception e) {
                    try {
                        TimeUnit.SECONDS.sleep(5);
                    } catch (InterruptedException interruptedException) {
                        Thread.currentThread().interrupt(); //传递 终端信号
                    }
                    isSuccess = timeOutCount++ > 3;
                    log.error("get datail data error !! ids:{}", stringList.toArray(), e);
                }
            }
        }
        BufferedWriter writer = new BufferedWriter(new FileWriter("/Users/<USER>/tw_data.json"));
        for (StatusPack.Status status : dataList) {
            String replace = ProtoBufFormatter.toJson(status).replace("\n", "");
            replace = replace.replace("\r","");
            replace = replace.replace("\n","");
            writer.write(replace);
            writer.newLine();
        }
        writer.flush();
        writer.close();
    }
    @Test
    public void test2() throws IOException {
        Properties properties = new Properties();

        //buka相关配置
        properties.put(FreonCommons.Buka.BOOTSTARP_SERVERS, "10.20.1.158:9096,10.20.1.159:9096");
        properties.put(FreonCommons.Buka.KEY_SERIALIZER, StringSerializer.class);
        properties.put(FreonCommons.Buka.VALUE_SERIALIZER, ByteArraySerializer.class);
        final KafkaProducer kafkaProducer = new KafkaProducer<String, byte[]>(properties);
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        List<StatusPack.WeMedia> dataList = new ArrayList<>();
        List<String> list = new ArrayList<>();
        BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/web_data_id.json"));
        String str = null;
        while ((str = reader.readLine()) != null) {
            list.add(str);
        }
//        list.add("si4742150179263764_1646100120");
        for (List<String> stringList : Lists.partition(list, 100)){
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            if (stringList.size() == 0) {
                break;
            }
            int timeOutCount = 0;
            boolean isSuccess = false;
            map.add("ids", StringUtil.join(stringList.iterator(), ","));
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
            Map<String,StatusPack.Document.Builder> dataMap = new HashMap<>();
            while (!isSuccess) {
                try {
                    ResponseEntity<byte[]> responseEntity = restTemplate.postForEntity("http://freon.dev-antducloud.com/document/batch/query", request, byte[].class);
                    if (responseEntity.getBody() != null && responseEntity.getBody().length != 0) {
                        List<StatusPack.Document> weMedia =
                            MessageSetUtils.fromByteArray(StatusPack.Document.class, responseEntity.getBody());
                        for (StatusPack.Document status : weMedia) {
                            dataMap.put(status.getDocId(),status.toBuilder());
                        }
//                        StatusPack.Status weMedia1 = weMedia.get(0);
//                        StatusPack.Status.Builder builder = weMedia1.toBuilder();
                        responseEntity = restTemplate.postForEntity("http://freon.dev-antducloud.com/tags/batch/query", request, byte[].class);
                        List<StatusPack.Tags> tags =
                            MessageSetUtils.fromByteArray(StatusPack.Tags.class, responseEntity.getBody());
                        for (StatusPack.Tags tag : tags) {
                            StatusPack.Document.Builder builder = dataMap.get(tag.getStatusId());
                            if (builder != null) {
                                builder.setTags(tag);
                            }
                        }
//                        StatusPack.Tags.Builder builder1 = tags.get(0).toBuilder();
//                        String tagLocation = builder1.getTagLocation() + " D10371000";
//                        builder1.setTagLocation(tagLocation);
                        responseEntity = restTemplate.postForEntity("http://freon.dev-antducloud.com/mutaprop/batch/query", request, byte[].class);
                        List<StatusPack.MutabilityProperties> mutabilityProperties =
                            MessageSetUtils.fromByteArray(StatusPack.MutabilityProperties.class,
                                                          responseEntity.getBody());
                        for (StatusPack.MutabilityProperties mutabilityProperty : mutabilityProperties) {
                            StatusPack.Document.Builder builder = dataMap.get(mutabilityProperty.getStatusId());
                            if(builder != null) {
                                builder.setProps(mutabilityProperty);
                            }
                        }
//                        builder.setTags(builder1.build());
//                        builder.setProps(mutabilityProperties.get(0));
                        for (Map.Entry<String, StatusPack.Document.Builder> stringBuilderEntry : dataMap.entrySet()) {

                            kafkaProducer.send(new ProducerRecord("webpage_event_tagged", stringBuilderEntry.getValue().build().toByteArray()));
                        }
                        kafkaProducer.flush();
                    }
                    isSuccess = true;
                } catch (Exception e) {
                    try {
                        TimeUnit.SECONDS.sleep(5);
                    } catch (InterruptedException interruptedException) {
                        Thread.currentThread().interrupt(); //传递 终端信号
                    }
                    isSuccess = timeOutCount++ > 3;
                    log.error("get datail data error !! ids:{}", stringList.toArray(), e);
                }
            }
        }
        BufferedWriter writer = new BufferedWriter(new FileWriter("/Users/<USER>/wemed_data.json"));
        for (StatusPack.WeMedia status : dataList) {
            String replace = ProtoBufFormatter.toJson(status).replace("\n", "");
            replace = replace.replace("\r","");
            replace = replace.replace("\n","");
            writer.write(replace);
            writer.newLine();
        }
        writer.flush();
        writer.close();
        kafkaProducer.flush();
        System.in.read();
    }
    @Test
    public void restore() throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        List<StatusPack.WeMedia> dataList = new ArrayList<>();
        Map<String,Integer> map = new HashMap<>();
        List<String> list = new LinkedList<>();
        BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/keyWordImage"));
        String str = null;
        while ((str = reader.readLine()) != null) {
            if ((str.charAt(0) == 'w' && str.charAt(1) == 'm')) {
                String[] split = str.split("-");
                if (split.length == 2) {
                    map.put(split[0], Integer.parseInt(split[1]));
                    list.add(split[0]);
                } else if (split.length < 2) {
                    list.add(str);
                } else {
                    System.out.println(str);
                }
            }

        }
        BufferedWriter writer = new BufferedWriter(new FileWriter("/Users/<USER>/keywordImages"));
        for (List<String> stringList : Lists.partition(list, 100)){
            MultiValueMap<String, String> mmap = new LinkedMultiValueMap<>();
            if (stringList.size() == 0) {
                break;
            }
            int timeOutCount = 0;
            boolean isSuccess = false;
            mmap.add("ids", StringUtil.join(stringList.iterator(), ","));
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(mmap, headers);

            while (!isSuccess) {
                try {
                    ResponseEntity<byte[]> responseEntity = restTemplate.postForEntity("http://freon.dev-antducloud.com/wemedia/batch/query", request, byte[].class);
                    if (responseEntity.getBody() != null && responseEntity.getBody().length != 0) {
                        List<StatusPack.WeMedia> weMedia =
                            MessageSetUtils.fromByteArray(StatusPack.WeMedia.class, responseEntity.getBody());
                        for (StatusPack.WeMedia status : weMedia) {
                            Integer pos = map.get(status.getWmId());
                            if (pos == null) {
                                writer.write(status.getPicUrl());
                            } else {
                                writer.write(status.getPicUrls(pos));
                            }
                            writer.newLine();
                        }
                    }
                    isSuccess = true;
                } catch (Exception e) {
                    try {
                        TimeUnit.SECONDS.sleep(5);
                    } catch (InterruptedException interruptedException) {
                        Thread.currentThread().interrupt(); //传递 终端信号
                    }
                    isSuccess = timeOutCount++ > 3;
                    log.error("get datail data error !! ids:{}", stringList.toArray(), e);
                }
            }
        }
        writer.flush();
        writer.close();
    }

    @Test
    public void formatWord() throws IOException {
        BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/Downloads/kws_dict"));
        BufferedWriter writer = new BufferedWriter(new FileWriter("/Users/<USER>/Downloads/kws_dict1"));
        String str = null;
        int i = 2026022;
        while ((str = reader.readLine()) != null) {
            String s = str.split("\t")[0];
            writer.write(String.valueOf(i++));
            writer.write(" ");
            writer.write(s);
            writer.write(" n 0");
            writer.newLine();
        }
        writer.flush();
        writer.close();
    }

    @Test
    public void tes() throws IOException {
        RestTemplate rest = new RestTemplate();
        ResponseEntity<Map> forEntity =
            rest.getForEntity("http://10.20.5.71:8379/percolator/topic/iterator", Map.class);
        Map body = forEntity.getBody();
        List<Map> list = (List<Map>) body.get("result");
        int count = 0;
        Set<String> query = new HashSet<>();
        for (Map map : list) {
            query.add((String) map.get("queryString"));
        }
        BufferedWriter writer = new BufferedWriter(new FileWriter("/Users/<USER>/hQuery"));
        for (String s : query) {
            writer.write(s);
            writer.newLine();
            if (count++ > 10000) {
                break;
            }
        }
        writer.flush();
        writer.close();
        System.out.println("x");
    }
}
