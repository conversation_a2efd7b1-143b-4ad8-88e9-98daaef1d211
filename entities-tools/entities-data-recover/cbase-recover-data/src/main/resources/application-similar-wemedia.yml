server:
  port: 8097

spring:
  application:
    name: recover-similar-wemedia-service

antdu:
  discovery:
    serverUrl:  http://metaserver.dev-antducloud.com/
  metrics:
    url:  http://metrics-prom.dev-antducloud.com/metric/report
    clusterName: recover-similar-wemedia-service
    slf4jEnable: false
    jmxEnable:  false
    httpEnable: false
    slf4jTimeInterval: 60
    httpTimeInterval: 60

eureka:
  instance:
    preferIpAddress: true
  client:
    enabled: true
    serviceUrl:
      defaultZone:  http://coordinator.dev-antducloud.com/eureka/v2/
    register-with-eureka: false

rate_limit:
  service: ratelimit-grpc-server
  max_request_sec: 100000
  deviation: 0.001
  time_widows_seconds: 1
  request_timeout_seconds: 10

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage
    classpath: com.antfact.commons.v2.model.StatusPack$WeMedia
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: wmId,contentId,platform,url,title,createdAt,content,userId,userName,nickName,userDescription,headUrl,vip,vipType,picUrl,videoUrl,audioUrl,mediaType,wmType,scope,channelId,channelName,countryCode,domain,expand,ip,ipLocation,latitude,longitude,location,originalContent,originalId,originalUserId,originalUserName,originalWm,props,source,tags,websiteName,tagsWithScore,important,authInfo,geoLocation,translation,ocrText,mcRelated
    field:
      primaryKey: wmId
      createat: createdAt

buka:
  bootstrapServers: yu-buka1:9096,yu-buka2:9096,yu-buka3:9096,yu-buka4:9096,yu-buka5:9096,yu-buka6:9096
  producerTopic: recorder-wemedia-data

cbase:
  keyspace: yuqing_twcs
  table: doc_view
  clusters:
    map:
      hot_cluster:
        enable: true
        clusterNodes: **********,**********,**********,**********,**********
        clusterName: yu-hot-cluster
        port: 9042
        datacenter: datacenter1
        username: cbase
        password: antducbaseadmin@2022
        operateTimeout: 30000
        ttlMaxDays: 14
        subTableDuration: 604800
        ttlMax: 8035200
        ttlMin: 604800
        compactType: twcs
        sequenceNo: 0

max:
  request:
    time_second:  1
    capacity: 100000

log:
  print:
    interval: 30000

persistence:
  rate_limit:
    resourceId: recover-wemedia-service
    qps: 30000

recover:
  begin: '20210103'
  end:  '20201201'
  platform: sina
  min_stamp: 1601481600000 #若数据createtime小于这个时间戳，则过滤

hstore:
  path: '/crawled/wemedia/data'

disruptor:
  producer:
    size: 1
  consumer:
    size: 1
  workpool:
    coreThread: 20
    threadQueueSize: 200
    keepAliveTime: 30

future:
  consumer:
    limit: 50000
    sleep:
      time: 5000

pool:
  maxRequest: 8192
  coreConn: 1
  maxConn: 2
  heartBeatInterval: 30