server:
  port: 8394

spring:
  application:
    name: recover-document-service

antdu:
  discovery:
    serverUrl:  http://metaserver.dev-antducloud.com/
  metrics:
    url:  http://metrics.dev-antducloud.com/metric/report
    clusterName: recover-document-service
    slf4jEnable: false
    jmxEnable:  false
    httpEnable: true
    slf4jTimeInterval: 60
    httpTimeInterval: 60

eureka:
  instance:
    preferIpAddress: true
  client:
    enabled: true
    serviceUrl:
      defaultZone:  http://coordinator.dev-antducloud.com/eureka/v2/
    register-with-eureka: false

rate_limit:
  service: ratelimit-grpc-server
  max_request_sec: 100000
  deviation: 0.001
  time_widows_seconds: 1
  request_timeout_seconds: 10

freon:
  entity:
    classpath: com.antfact.commons.v2.model.StatusPack$Tags
    field:
      primaryKey: statusId
      createat: createAt

buka:
  bootstrapServers: yu-buka1:9096,yu-buka2:9096,yu-buka3:9096,yu-buka4:9096,yu-buka5:9096,yu-buka6:9096
  producerTopic: recorder-document-data

cbase:
  keyspace: yuqing_skinny
  table: tags
  clusters:
    map:
      cold_cluster:
        enable: true
        clusterNodes: *********,*********,*********,*********,*********
        clusterName: opinion-offcut-cold-cluster
        port: 9042
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 604800

max:
  request:
    time_second:  1
    capacity: 100000

log:
  print:
    interval: 60000

persistence:
  rate_limit:
    resourceId: recover-document-service
    qps: 200000

recover:
  begin: '2020-04-12'
  end:  '2020-04-11'
  platform: sina
  min_stamp: 1581955200000 #若数据createtime小于这个时间戳，则过滤 2-18~2-24