server:
  port: 8564

spring:
  application:
    name: recover-indexer-status-service

antdu:
  discovery:
    serverUrl:  http://metaserver.dev-antducloud.com/
  metrics:
    url:  http://metrics.dev-antducloud.com/metric/report
    clusterName: recover-indexer-status-service
    slf4jEnable: false
    jmxEnable:  false
    httpEnable: false
    slf4jTimeInterval: 60
    httpTimeInterval: 60

eureka:
  instance:
    preferIpAddress: true
  client:
    enabled: true
    serviceUrl:
      defaultZone:  http://coordinator.dev-antducloud.com/eureka/v2/
    register-with-eureka: false

rate_limit:
  service: ratelimit-grpc-server
  max_request_sec: 100000
  deviation: 0.001
  time_widows_seconds: 1
  request_timeout_seconds: 10

freon:
  entity:
    classpath: com.antfact.commons.v2.model.StatusPack$Status
    field:
      primaryKey: statusId
      createat: createAt

buka:
  bootstrapServers: yu-buka1:9096,yu-buka2:9096,yu-buka3:9096,yu-buka4:9096,yu-buka5:9096,yu-buka6:9096
  producerTopic: recorder-indexer-status-data

cbase:
  keyspace: yuqing_skinny
  table: wemedia
  clusters:
    map:
      cold_cluster:
        enable: true
        clusterNodes: *********,*********,*********,*********,*********,*********0,*********8
        clusterName: opinionCbase
        port: 9042
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 604800

max:
  request:
    time_second:  1
    capacity: 100000

log:
  print:
    interval: 60000

persistence:
  rate_limit:
    resourceId: recover-indexer-status-service
    qps: 200000

recover:
  begin: 2020-02-11
  end:  2020-02-12
  platform: sina
  min_stamp: 1581350400000 #若数据createtime小于这个时间戳，则过滤

#hstore:
#  path: '/crawled/wemedia/data'

#disruptor:
#  producer:
#    size: 1
#  consumer:
#    size: 1
#  workpool:
#    maxThread: 1
#    threadQueueSize: 1
#    keepAliveTime: 1
cluster:
  es:
    clusters:
      - indexer_mblog_history:
          clusterName: indexer_mblog_history
          clusterNodes: 10.20.1.120,10.20.1.121,10.20.1.122
          port: 9600
indexer:
  timeout: 10
  mapping:
    micro_blog_history_twitter:
      cluster: indexer_mblog_history
      index: twitter
      indexAlias: twitter
      indexType: _doc
      indexPattern: yyyy.MM.dd
      indexInterval: 24
      indexTTL: 91D
      timeField: createdAt
      uidName: statusId
      clusterType: weibo
      platform: twitter
      needRecover: true
    micro_blog_history_sina:
      cluster: indexer_mblog_history
      index: weibo
      indexAlias: weibo
      indexType: _doc
      indexPattern: yyyy.MM.dd
      indexInterval: 24
      indexTTL: 91D
      timeField: createAt
      uidName: statusId
      clusterType: weibo
      platform: sina
      needRecover: true

index:
  mapping:
    name: micro_blog_history_sina
  zk:
    hosts: p-zk01:2171,p-zk02:2171,p-zk03:2171
