package com.antdu.freon.recover.core.runable.filter;

import com.antfact.commons.v2.model.TopicPack;
import com.antfact.commons.v2.model.UserPack;

import org.apache.logging.log4j.util.Strings;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

/**
 * 创建日期 4/10/23
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Component("weMediaUserNickNameFilter")
@ConditionalOnExpression("#{environment['generic.filter'] != null && environment['generic.filter'].contains('weMediaUserNickNameFilter')}")
public class WeMediaUserNickNameFilter extends AbsSourceFilter<UserPack.User> {

    /**
     * 快手过滤用户id正则表达式
     */
    public static final String regExp = "wmks[0-9]*";

    @SuppressWarnings("checkstyle:HiddenField")
    @Override
    public boolean test(UserPack.User user) {
        if ("".equals(user.getNickName())) {
            return false;
        }
        //抖音没有blogURL 过滤
        return !user.getPlatform().equalsIgnoreCase(TopicPack.Platform.WM_DOUYIN.name());
    }
}
