/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.recover.core.apollo;

import com.antdu.freon.disruptor.core.MessageConsumer;
import com.antdu.freon.disruptor.core.RingBufferWorkerPoolFactory;
import com.antdu.freon.recover.core.disruptor.RecoverDataConsumer;
import com.antdu.microservice.chassis.dconfig.api.DConfig;
import com.antdu.microservice.chassis.dconfig.apollo.listener.ApolloConfigChangeListener;
import com.antdu.microservice.chassis.dconfig.listener.DConfigChangeEvent;
import com.antdu.microservice.chassis.dconfig.service.DConfigService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

import java.util.Collection;

import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>recover 动态调整限流速度</p>
 * <p/>
 * 创建日期 2020/11/19
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
@Configuration
@ConditionalOnExpression("${dapollo.bootstrap.enabled:true}&&${persistence.rate_limit.enable:true}")
public class AdjustRaterlimiterConfiguration {

    /**
     * 配置服务
     */
    @Autowired
    private DConfigService dConfigService;

    /**
     * ringbuffer 工厂
     */
    @Autowired
    private RingBufferWorkerPoolFactory ringBufferWorkerPoolFactory;

    /**
     * 配置文件namespace
     */
    @Value("${dapollo.bootstrap.namespaces}")
    private String dapolloNamespaces;

    /**
     * recover 消费集合
     */
    private Collection<MessageConsumer> consumers;

    /**
     * 初始化限流调整监听
     */
    @PostConstruct
    private void ratelimiterChangeLinstener() {
        consumers = ringBufferWorkerPoolFactory.getConusmerCollect();
        if (null == consumers || consumers.isEmpty()) {
            return;
        }
        DConfig dConfig = dConfigService.getConfig(dapolloNamespaces.split(",")[0]);
        dConfig.addChangeListener(new RatelimitDConfigChangeListener());
    }

    /**
     * 限流更改监听器
     */
    private class RatelimitDConfigChangeListener extends ApolloConfigChangeListener {
        @Override
        public void onChange(DConfigChangeEvent changeEvent) {
            changeEvent.changedKeys().forEach(changedKey -> {
                if (!"persistence.rate_limit.qps".equals(changedKey)) {
                    return;
                }
                MessageConsumer consumer = consumers.iterator().next();
                if (consumer instanceof RecoverDataConsumer) {
                    ((RecoverDataConsumer) consumer).adjustSpeed(
                        Integer.parseInt(changeEvent.getChange(changedKey).getNewValue()));
                }
            });
        }
    }
}
