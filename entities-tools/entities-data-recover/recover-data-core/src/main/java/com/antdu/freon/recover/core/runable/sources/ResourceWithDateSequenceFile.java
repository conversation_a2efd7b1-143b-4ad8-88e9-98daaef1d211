/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.recover.core.runable.sources;

import com.antfact.mapreduce.google.protobuf.Message;

import com.antdu.hadoop.hdfs.Reader;
import com.antdu.hstore.files.abstraction.ProtobufSequenceFiles;
import com.antdu.hstore.reader.HStoreReader;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2021/7/29
 * 读取带时间的document文件
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
@Component("resourceWithDateSequenceFile")
@ConditionalOnExpression
    ("#{environment['generic.source'] != null && environment['generic.source'].equals('resourceWithDateSequenceFile')}")
public class ResourceWithDateSequenceFile extends AbsSourceGet<Iterable<Message>> {
    /**
     * protobuf实体路径名 如:com.antfact.commons.v2.model.StatusPack$Status
     */
    @Value("${freon.entity.classpath}")
    private String className;

    @Override
    protected Iterable<Message> getSource() {
        try {
            Reader reader = new HStoreReader();
            isOneTime = true;
            return reader.read(new ProtobufSequenceFiles<>(className, hstorePath, beginDate, endDate));
        } catch (Exception e) {
            log.error("read v9 document file error !!! ", e);
        }
        return null;
    }
}
