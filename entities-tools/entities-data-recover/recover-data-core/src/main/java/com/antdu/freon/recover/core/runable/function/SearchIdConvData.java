/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.recover.core.runable.function;

import com.antfact.commons.v2.MessageSetUtils;
import com.antfact.mapreduce.google.protobuf.AbstractMessage;
import com.antfact.mapreduce.google.protobuf.Message;

import com.google.common.collect.Lists;

import org.jsoup.helper.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2021/8/10
 * 索引拿出 id 构建数据
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
@Component("searchIdConData")
@ConditionalOnExpression
    ("#{environment['generic.converter'] != null && environment['generic.converter'].contains('searchIdConData')}")
public class SearchIdConvData extends AbsSourceConv<List<String>, List<Message>> {
    /**
     * partition Size
     */
    public static final int PARTITION_SIZE = 100;
    /**
     * protobuf实体路径名 如:com.antfact.commons.v2.model.StatusPack$Status
     */
    @Value("${freon.entity.classpath}")
    private String className;
    /**
     * cbase 详情查询入口
     */
    @Value("${freon.query.url:http://freon.dev-antducloud.com/status/batch/query}")
    private String query;
    /**
     * restTemplate
     */
    @Autowired
    protected RestTemplate restTemplate;
    /**
     * http header
     */
    private HttpHeaders headers;
    /**
     * Message class
     */
    private Class<?> clazz;

    /**
     * init
     *
     * @throws ClassNotFoundException e
     */
    @PostConstruct
    public void init() throws ClassNotFoundException {
        clazz = Class.forName(className);
        headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    }


    @SneakyThrows
    @Override
    public List<Message> apply(List<String> strings) {
        List<Message> dataList = new LinkedList<>();
        if (strings.size() == 0) {
            return dataList;
        }
        try {
            getDetail(dataList, strings, PARTITION_SIZE, headers, restTemplate, query, clazz);
        } catch (Exception e) {
            log.error("query detail error", e);
        }
        return dataList;
    }

    /**
     * 获取详情
     *
     * @param dataList      输出 数据
     * @param ids           ids
     * @param partitionSize 分区Size
     * @param headers       http头
     * @param restTemplate  http client
     * @param url           url
     * @param clazz         protobuf 对象
     */
    @SuppressWarnings("checkstyle:MagicNumber")
    public static void getDetail(List<Message> dataList, List<String> ids, int partitionSize,
                                 HttpHeaders headers, RestTemplate restTemplate,
                                 String url, Class clazz) {
        for (List<String> stringList : Lists.partition(ids, partitionSize)) {
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            if (stringList.size() == 0) {
                break;
            }
            int timeOutCount = 0;
            boolean isSuccess = false;
            map.add("ids", StringUtil.join(stringList.iterator(), ","));
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
            while (!isSuccess) {
                try {
                    ResponseEntity<byte[]> responseEntity = restTemplate.postForEntity(url, request, byte[].class);
                    if (responseEntity.getBody() != null && responseEntity.getBody().length != 0) {
                        dataList.addAll(MessageSetUtils.fromByteArray(clazz, responseEntity.getBody()));
                    }
                    isSuccess = true;
                } catch (Exception e) {
                    try {
                        TimeUnit.SECONDS.sleep(5);
                    } catch (InterruptedException interruptedException) {
                        Thread.currentThread().interrupt(); //传递 终端信号
                    }
                    isSuccess = timeOutCount++ > 3;
                    log.error("get datail data error !! ids:{}", stringList.toArray(), e);
                }
            }
        }
    }
}
