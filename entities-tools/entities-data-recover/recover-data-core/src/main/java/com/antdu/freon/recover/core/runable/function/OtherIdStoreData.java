/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.recover.core.runable.function;



import com.antfact.mapreduce.google.protobuf.Descriptors;
import com.antfact.mapreduce.google.protobuf.Message;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2021/8/25
 * 描述: 指定单独 数据字段保留
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
@Component("otherIdStoreData")
@ConditionalOnExpression
    ("#{environment['generic.converter'] != null && environment['generic.converter'].contains('otherIdStoreData')}")
public class OtherIdStoreData extends AbsSourceConv<List<Message>, List<Message>> {
    /**
     * fileName
     */
    @Value("${generic.id.fileName}")
    private String fileName;
    /**
     * 实体字段详情 映射
     */
    @Autowired
    private Map<String, Descriptors.FieldDescriptor> descriptorMap;
    /**
     * field Name
     */
    @Value("${generic.id.fieldName}")
    public String fieldName;
    /**
     * fieldDesc
     */
    Descriptors.FieldDescriptor fieldDescriptor;
    /**
     * bufferWrite
     */
    private BufferedWriter writer;
    /**
     * init
     *
     * @throws ClassNotFoundException e
     */
    @PostConstruct
    public void init() throws IOException {
        writer = new BufferedWriter(new FileWriter(fileName));
        fieldDescriptor = descriptorMap.get(fieldName);
    }


    @SneakyThrows
    @Override
    public List<Message> apply(List<Message> strings) {
        for (Message string : strings) {
            writer.write(string.getField(fieldDescriptor).toString());
            writer.newLine();
        }
        writer.flush();
        return strings;
    }

    /**
     * close stream
     * @throws IOException
     */
    @PreDestroy
    public void close() throws IOException {
        if (writer != null) {
            writer.flush();
            writer.close();
        }
    }
}
