/*
 * Copyright © 2023 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2023湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.recover.core.runable.sources;

import com.antfact.commons.v2.model.StatusPack;
import com.antfact.datacenter.batch.job.data.DocData;
import com.antfact.datacenter.batch.job.help.OrcMrHelper;

import com.antdu.freon.recover.core.runable.sources.hfile.OrcFilesReader;
import com.antdu.freon.recover.core.runable.sources.hfile.OrcStructFiles;

import org.apache.orc.mapred.OrcStruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>自媒体 规整Orc文件数据源 </p>
 * <p>
 * 创建日期： 2023/6/26
 *
 * <AUTHOR> tiant<PERSON>(<EMAIL>)
 */
@Slf4j
@Component("wemediaOrcSource")
@ConditionalOnExpression
    ("#{environment['generic.source'] != null && environment['generic.source'].equals('wemediaOrcSource')}")
public class WemediaOrcSource extends AbsSourceGet<Iterable<StatusPack.WeMedia>> {

    /**
     * 固定字段，只读取指定字段
     */
    @Value("${freon.entity.messageFields.fixed:}")
    private String fixedField;

    @Override
    protected Iterable<StatusPack.WeMedia> getSource() {
        isOneTime = true;
        try {
            return new OrcFilesReader<>(new OrcStructFiles<StatusPack.WeMedia>(hstorePath, beginDate, endDate) {
                @Override
                public StatusPack.WeMedia covert(Object o) {
                    OrcStruct struct = (OrcStruct) o;

                    Boolean fromProperty = OrcMrHelper.getBoolean(struct, DocData.COL_IDX_FROM_PROPERTY);
                    if (fromProperty != null && fromProperty) { //是属性数据
                        return null;
                    }
                    if (fixedField.equals("wmId,createdAt")) { //原帖id时间映射，只读取指定字段
                        String docId = OrcMrHelper.getString(struct, DocData.COL_IDX_DOC_ID);
                        Long createdAt = OrcMrHelper.getLong(struct, DocData.COL_IDX_CREATED_AT);
                        Integer stateType = OrcMrHelper.getInt(struct, DocData.COL_IDX_STATUS_TYPE);
                        if (createdAt == null || stateType == null) {
                            return null;
                        }
                        return StatusPack.WeMedia.newBuilder()
                                                 .setWmId(docId).setCreatedAt(createdAt)
                                                 .setWmTypeValue(stateType)
                                                 .build();
                    }
                    DocData docData = DocData.from(struct);
                    return DocData.toWemedia(docData);
                }

                @Override
                public List<Integer> readFieldIndexes() {
                    if (fixedField.equals("wmId,createdAt")) {
                        return Arrays.asList(DocData.COL_IDX_DOC_ID, DocData.COL_IDX_CREATED_AT,
                                             DocData.COL_IDX_STATUS_TYPE, DocData.COL_IDX_FROM_PROPERTY);
                    }
                    return null;
                }
            }, fileIsLocal);
        } catch (Exception e) {
            log.error("read wemedia orc file error !!! ", e);
        }
        return null;
    }
}
