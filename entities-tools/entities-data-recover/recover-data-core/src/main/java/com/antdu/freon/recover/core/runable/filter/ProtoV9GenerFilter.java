/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.recover.core.runable.filter;

import com.antfact.mapreduce.google.protobuf.Descriptors;
import com.antfact.mapreduce.google.protobuf.Message;

import com.antdu.freon.commons.utils.ClockUtils;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 创建日期 2021/7/28
 * protobuff 对象过滤
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Component("protoV9GenerFilter")
@ConditionalOnExpression("#{environment['generic.filter'] != null && environment['generic.filter'].contains('protoV9GenerFilter')}")
@SuppressWarnings({"checkstyle:MagicNumber", "lineLength"})
public class ProtoV9GenerFilter extends AbsSourceFilter<Message> {

    /**
     * 实体字段详情 映射
     */
    @Autowired
    private Map<String, Descriptors.FieldDescriptor> descriptorMap;
    /**
     * 上限
     */
    @Value("${recover.max_stamp:0}")
    private long maxTimestamp;
    /**
     * protobuf 创建时间字段
     */
    @Value("${freon.entity.field.createat:}")
    private String createAtField;

    @Override
    public boolean test(Message message) {
        //过滤条件: 1. message 创建时间符合时间戳格式 1. message创建时间<允许写入的最小时间戳
        long createAt = StringUtils.isBlank(createAtField)
                        ? ClockUtils.now()
                        : (Long) message.getField(descriptorMap.get(createAtField));
        return Long.toString(createAt).length() == 13 && createAt >= minTimestamp && createAt <= maxTimestamp;
    }
}
