/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.recover.core.runable.sources;

import com.antfact.mapreduce.google.protobuf.Message;

import com.antdu.freon.recover.core.runable.sources.hfile.SequenceFilesToProto;
import com.antdu.hadoop.hdfs.Reader;
import com.antdu.hstore.reader.HStoreReader;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2021/7/29
 * 读取固定合成的文件（详情数据和tags+prop的组合数据)
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
@Component("resourceSequenceFileWithFilter")
@ConditionalOnExpression
    ("#{environment['generic.source'] != null && environment['generic.source'].equals('resourceSequenceFileWithFilter')}")
public class ResourceSequenceFileWithFilter extends AbsSourceGet<Iterable<Message>> {
    /**
     * protobuf实体路径名 如:com.antfact.commons.v2.model.StatusPack$Status
     */
    @Value("${freon.entity.classpath}")
    private String className;

    @Override
    protected Iterable<Message> getSource() {
        try {
            Reader reader = new HStoreReader();
            isOneTime = true;
            return reader.read(new SequenceFilesToProto<>(hstorePath, beginDate, endDate, className));
        } catch (Exception e) {
            log.error("read v9 document file error !!! ", e);
        }
        return null;
    }
}
