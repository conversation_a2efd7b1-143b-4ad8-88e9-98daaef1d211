/*
 * Copyright © 2020 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2020湖南蚁为软件有限公司。保留所有权利。
 */


package com.antdu.freon.restore.indexer;

import com.antdu.gaia.index.config.IndexAutoConfiguration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 创建日期 2020/11/5
 *
 * <AUTHOR> @ eefung.com)
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.antdu.freon.restore.indexer", "com.antdu.image"},
    scanBasePackageClasses = {IndexAutoConfiguration.class},
    exclude = {})
public class ImageCodeIndexDataRecover {
    public static void main(String... args) {
        SpringApplication.run(ImageCodeIndexDataRecover.class, args);
    }
}
