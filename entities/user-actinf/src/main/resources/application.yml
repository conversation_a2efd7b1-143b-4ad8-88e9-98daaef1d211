spring.application.name: user-actinf-service-dev
server.port: 8383

storage.filter.maxkeys: 10000

storage.local.dataPath: ./
buka.actinf-update:
  topics: user_activity_influence_update,wemedia_user_activity_influence_update
  group: user_activity_influence_storage_local

log.print.interval: 1000

#=============: kafka ===================
# 指定kafka 代理地址，可以多个
spring.kafka:
  bootstrap-servers: yu-buka1:9096,yu-buka2:9096,yu-buka3:9096,yu-buka4:9096
#==============: consumer  =======================
# 指定默认消费者group id
  consumer:
    group-id: user_buka_actinf
    auto-offset-reset: earliest
    enable-auto-commit: true
    auto-commit-interval: 5000
#    max-poll-records: 100
# 指定消息key和消息体的编解码方式
    key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    value-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
#==============: Listener  =======================
  listener:
    concurrency: 1
    poll-timeout: 5000

#=============服务发现配置 =============
antdu:
  discovery:
    serverUrl: http://metaserver.dev-antducloud.com/
  metrics:
    clusterName: user-actinf-storage-local
    url: http://metrics-prom.dev-antducloud.com/metric/report
    slf4jEnable: false
    jmxEnable:  false
    httpEnable: true
    slf4jTimeInterval: 60
    httpTimeInterval: 60
eureka:
  instance:
    preferIpAddress: true
  client:
    enabled: true
    serviceUrl:
      defaultZone:  http://coordinator.dev-antducloud.com/eureka/v2/

#====================  数据初始化读hdfs   =========================
file:
  filePaths:
    - platform: sina
      path: /oozie-tasks-v2/activity-influence-to-buka/sina/history
# 不设置开始结束，默认所有
#      fileStart: all-r-00000
#      fileEnd: all-r-00011
    - platform: twitter
      path: /oozie-tasks-v2/activity-influence-to-buka/twitter/history
    - platform: wemedia
      path: /oozie-tasks-v2/activity-influence-to-buka-wemedia/wemedia/history
      addPrefix: false
  readSpeed: 20
  readSleepTime: 5000

#启用HTTP2
server:
  compression.enabled: false
  http2.enabled: false
  ssl:
    enabled: false
    key-store: classpath:keystore.p12
    key-store-password: eefung
    key-store-type: PKCS12
    protocol: TLSv1.2
    key-alias: actinf