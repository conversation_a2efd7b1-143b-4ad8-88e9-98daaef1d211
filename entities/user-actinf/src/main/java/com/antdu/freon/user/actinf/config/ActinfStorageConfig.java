/*
 * Copyright © 2019 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2019湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.user.actinf.config;

import com.antdu.freon.user.actinf.HDFSDataReader;
import com.antdu.freon.user.actinf.entity.DiffSpaceFilterStorage;
import com.antdu.freon.user.actinf.entity.FilterStorage;
import com.antdu.freon.user.actinf.util.DataFileUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.io.FileNotFoundException;

import lombok.extern.slf4j.Slf4j;

import static com.antdu.freon.user.actinf.util.DataFileUtil.ACTIVITY_DATA_FILE;
import static com.antdu.freon.user.actinf.util.DataFileUtil.INFLUENCE_DATA_FILE;

/**
 * <p>存储配置 </p>
 * <p>
 * 创建日期： 2019/6/12
 *
 * <AUTHOR> @ eefung.com)
 */
@Configuration
@Slf4j
public class ActinfStorageConfig {
    /**
     * 本地数据文件名称
     */
    @Value("${storage.local.dataPath}")
    private String localDataPath = "./";
    /**
     * CuckooFilter maxkeys
     */
    @Value("${storage.filter.maxkeys}")
    private long maxkeys;
    /**
     * CuckooFilter maxkeys
     */
    @Value("${storage.filter.activity.smallspace.maxkeys:0}")
    private long activitySmallSpaceMaxkeys;
    /**
     * CuckooFilter maxkeys
     */
    @Value("${storage.filter.activity.largespace.maxkeys:0}")
    private long activityLargeSpaceMaxkeys;
    /**
     * CuckooFilter maxkeys
     */
    @Value("${storage.filter.influence.smallspace.maxkeys:0}")
    private long influenceSmallSpaceMaxkeys;
    /**
     * CuckooFilter maxkeys
     */
    @Value("${storage.filter.influence.largespace.maxkeys:0}")
    private long influenceLargeSpaceMaxkeys;
    /**
     * 活跃度存储
     */
    private FilterStorage activityStorage;
    /**
     * 影响力存储
     */
    private FilterStorage influenceStorage;
    /**
     * hdfs数据读取
     */
    @Autowired
    private HDFSDataReader hdfsDataReader;

    /**
     * 从本地文件恢复存储对象
     *
     * @return 是否从本地文件读取
     */
    @Bean(name = "storageInited")
    public boolean storageInited() {
        try {
            log.info("start read storage from local file:[{}] ...", DataFileUtil.getPath(localDataPath));
            activityStorage = DataFileUtil.readDataFromFile(localDataPath, ACTIVITY_DATA_FILE);
            influenceStorage = DataFileUtil.readDataFromFile(localDataPath, INFLUENCE_DATA_FILE);
            if (activityStorage != null && influenceStorage != null) {
                log.info("read activity and influence storage from local file success.");
                activityStorage.printInfo("activityStorage");
                influenceStorage.printInfo("influenceStorage");
                return true;
            }
        } catch (FileNotFoundException e) {
            log.error("local file not found.", e);
        }
        // 有一个数据读取本地文件不成功，则从hdfs读取初始化数据
        activityStorage = newFilterStorage(activitySmallSpaceMaxkeys, activityLargeSpaceMaxkeys);
        influenceStorage = newFilterStorage(influenceSmallSpaceMaxkeys, influenceLargeSpaceMaxkeys);
        log.info("read file fail, create activity and influenceStorage storage success.");
        hdfsDataReader.setStorage(activityStorage, influenceStorage);
        hdfsDataReader.start();
        return false;
    }

    /**
     * 活跃度存储bean
     *
     * @return
     */
    @Bean
    @DependsOn("storageInited")
    public FilterStorage activityStorage() {
        return activityStorage == null ? newFilterStorage(activitySmallSpaceMaxkeys, activityLargeSpaceMaxkeys)
                                       : activityStorage;
    }

    /**
     * 影响力存储bean
     *
     * @return
     */
    @Bean
    @DependsOn("storageInited")
    public FilterStorage influenceStorage() {
        return influenceStorage == null ? newFilterStorage(influenceSmallSpaceMaxkeys, influenceLargeSpaceMaxkeys)
                                        : influenceStorage;
    }

    /**
     * 根据keys最大数目参数构建filter storage
     *
     * @return
     */
    private FilterStorage newFilterStorage(long smallSpaceMaxkeys, long largeSpaceMaxkeys) {
        return smallSpaceMaxkeys <= 0 || largeSpaceMaxkeys <= 0
               ? new DiffSpaceFilterStorage(maxkeys)
               : new DiffSpaceFilterStorage(smallSpaceMaxkeys, largeSpaceMaxkeys);
    }
}
