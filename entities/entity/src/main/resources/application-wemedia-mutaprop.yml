server:
  port: 8944

route:
  rest: true
  path: mutaprop

spring:
  application:
    name: freon-message-wemedia-mutaprop

antdu:
  metrics:
    clusterName: freon-message-wemedia-mutaprop

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.special.MutabilityPropertiesStorage
    classpath: com.antfact.commons.v2.model.StatusPack$MutabilityProperties
    hasTimeStampQuery: true
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: statusId,repostsCount,commentsCount,state,favourCount,indexedAt,crawlTime,crawler,createAt,source,followerCount,readCount,editTime,watchCount,sharesCount,favoritesCount
    field:
      stateType: statusType
      primaryKey: statusId
      createat: createAt

properties:
  storage:
    buka:
      producerTopic: message-wemedia-mutaprop-topic
    jobs:
      cbase_hot:
        runnable: false
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.ignore.IgnoreTimeStampMutapropStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_property_dedupped_1
            group: message-wemedia-mutaprop-group-cbase-hot-test1
            persistId: persistence-message-wemedia-mutaprop-cbase-hot
            persistLimitSpeed: 30000
          deflect:
            threadCount: 0
            topic: message-wemedia-mutaprop-topic-deflect-cbase-hot
            group: message-wemedia-mutaprop-group-deflect-cbase-hot
            persistId: persistence-message-wemedia-mutaprop-cbase-hot-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 0
            topic: message-wemedia-mutaprop-topic-failback-cbase-hot
            group: message-wemedia-mutaprop-group-failback-cbase-hot
            persistId: persistence-message-wemedia-mutaprop-cbase-hot-failback
            persistLimitSpeed: 30000
      cbase_cold:
        runnable: false
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.MutabilityPropertiesStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_property_dedupped_1
            group: message-wemedia-mutaprop-group-cbase-cold1
            persistId: persistence-message-wemedia-mutaprop-cbase-cold
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-wemedia-mutaprop-topic-deflect-cbase-cold
            group: message-wemedia-mutaprop-group-deflect-cbase-cold
            persistId: persistence-message-wemedia-mutaprop-cbase-cold-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-wemedia-mutaprop-topic-failback-cbase-cold
            group: message-wemedia-mutaprop-group-failback-cbase-cold
            persistId: persistence-message-wemedia-mutaprop-cbase-cold-failback
            persistLimitSpeed: 30000
      caster:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.storage.caster.storage.special.MutapropCasterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_property_dedupped_1
            group: message-wemedia-mutaprop-group-caster-dev
            persistId: persistence-message-wemedia-mutaprop-caster
            persistLimitSpeed: 30000
          deflect:
            threadCount: 6
            topic: message-wemedia-mutaprop-topic-deflect-caster
            group: message-wemedia-mutaprop-group-deflect-caster-dev
            persistId: persistence-message-wemedia-mutaprop-caster-deflect
            persistLimitSpeed: 30000

cbase:
  keyspace: yuqing_skinny
  table: property_tags
  clusters:
    map:
      hot_cluster:
        enable: false
        clusterNodes: 10.20.2.22,10.20.2.23
        clusterName: Hot Cluster
        port: 9042
        datacenter: cs01
        username: cbase
        password: antducbaseadmin@2022
        operateTimeout: 60000
        ttlMaxDays: 2
        ttlMax: 172800
        ttlMin: 3600
        sequenceNo: 0
      cold_cluster:
        enable: true
        clusterNodes: 10.20.1.108,10.20.1.117,10.20.1.120,***********
        clusterName: property-tags-cluster-cold
        port: 9042
        datacenter: dc-opinion
        username: cbase
        password: antducbaseadmin@2022
        operateTimeout: 60000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 86400
        keyHasTimeStamp: true
        sequenceNo: 1

cache:
  name_pre: message.mutaprop.

caster.enable: false

caster-clusters:
  map:
    readCaster:
      enable: false
      clusterName: redis-yuqing-reader-mutaprop
      clusterAddress: **********:6720,**********:7722,**********:7720,**********:6721,**********:7721,**********:6722
      redisCodec: org.redisson.client.codec.ByteArrayCodec
      redisThreads: 16
      redisNettyThreads: 32
      nodeConnectionPoolSize: 64
      nodeConnectionMinSize: 5
      decodeInexecutor: true
      transportModeEPOLL: false
      timeout: 10000

readCache:
  readStringCache: false
  casterName: readCaster #是caster-clusters中配置的名字

writeCache:
  enable: false

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: wemedia-mutaprop-reader-ratelimiter

persist:
  executor:
    max_thread: 3