server:
  port: 8392

route:
  rest: false
  sortedset: false
  path: opinion-collect-harmful

spring:
  application:
    name: freon-harmful-opinion-collect-counter

antdu:
  metrics:
    clusterName: freon-harmful-opinion-collect-counter
    httpEnable: true

rate_limit:
  max_request_sec: 1000000

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.storage.caster.storage.opinion.HarmfulOpinionCollectCounterStorage
    classpath: com.antfact.commons.v2.model.OpinionWithTopicsPackage
    field:
      primaryKey: opinions

properties:
  storage:
    buka:
      bootstrapServers: buka1:9096,buka2:9096,buka3:9096,buka4:9096,buka5:9096
      enableAutoCommit: true
      offsetCommitFrequency: 30
      deflectLagThreshold: 50000000
      producerTopic: message-harmful-opinion-collect-counter-caster
    jobs:
      caster:
        runnable: true
        pollIsBatch: false
        acknowledgement: true
        className: com.antdu.freon.storage.caster.storage.opinion.HarmfulOpinionCollectCounterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 1
            topics:
              - pubopinion_topics_harmful_detail
            group: message-harmful-opinion-collect-group-caster
            persistId: persistence-harmful-opinion-collect-caster
            persistLimitSpeed: 500000
          deflect:
            threadCount: 1
            topic: message-opinion-collect-topic-deflect-caster
            group: message-opinion-collect-group-deflect-caster
            persistId: persistence-message-opinion-collect-deflect-caster
            persistLimitSpeed: 30000
          failback:
            threadCount: 1
            topic: message-harmful-opinion-collect-topic-failback-caster
            group: message-harmful-opinion-collect-group-failback-caster
            persistId: persistence-harmful-opinion-collect-failback-caster
            persistLimitSpeed: 30000

persist:
  executor:
    max_thread: 6

query.pagelimit: 10000

log:
  print:
    interval: 60000

caster:
  enable: true
  clusterName: redis-similar-store-count-wemedia
  clusterAddress: **********:6787
  redisCodec: org.redisson.client.codec.StringCodec
#  nodeConnectionMinSize: 1

#中小网站舆情计数前缀
collect.count.prefix: harmful.count.

caster-clusters:
  map:
    readCaster:
      enable: false
readCache:
  enable: false

writeCache:
  enable: false