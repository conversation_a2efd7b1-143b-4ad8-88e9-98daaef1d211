apiVersion: v1
kind: Service
metadata:
  name: freon-user-service
spec:
  selector:
    app: freon-user-rest
  ports:
    - name: rest-port
      protocol: TCP
      port: 8864
      targetPort: 8864
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-user-rest
  namespace: 
  labels:
    app: freon-user-rest
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-user-rest
  template:
    metadata:
      labels:
        app: freon-user-rest
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
      - name: freon-user-rest
        image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
        imagePullPolicy: Always
        args:
          - "-Ddapollo.bootstrap.namespaces=freon-user.yml,application-commons.yml"
          - "-Dapp_name=freon-user-rest"
          - "-Droute.rest=true"
          - "-XX:+UnlockExperimentalVMOptions"
          - "-XX:+UseCGroupMemoryLimitForHeap"
          - "-Dappend_ref=file"
          - "-Dlogging.config=config/log4j2.xml"
        ports:
        - name: rest-port
          containerPort: 8864
        volumeMounts:
        - name: rest-config
          mountPath: /home/<USER>
      volumes:
      - name: rest-config
        configMap:
          name: freon-message-entities-cm
---
apiVersion: v1
kind: Service
metadata:
  name: freon-wemedia-user-service
spec:
  selector:
    app: freon-wemedia-user-rest
  ports:
    - name: rest-port
      protocol: TCP
      port: 8864
      targetPort: 8864
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-wemedia-user-rest
  namespace:
  labels:
    app: freon-wemedia-user-rest
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-wemedia-user-rest
  template:
    metadata:
      labels:
        app: freon-wemedia-user-rest
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-wemedia-user-rest
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-Ddapollo.bootstrap.namespaces=wemedia-user-rest.yml,application-commons.yml"
            - "-Dapp_name=freon-wemedia-user-rest"
            - "-Droute.rest=true"
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          ports:
            - name: rest-port
              containerPort: 8864
          volumeMounts:
            - name: rest-config
              mountPath: /home/<USER>
      volumes:
        - name: rest-config
          configMap:
            name: freon-message-entities-cm
---
apiVersion: v1
kind: Service
metadata:
  name: freon-user-history-service
spec:
  selector:
    app: freon-user-history-rest
  ports:
    - name: rest-port
      protocol: TCP
      port: 8864
      targetPort: 8864
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-user-history-rest
  namespace:
  labels:
    app: freon-user-history-rest
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-user-history-rest
  template:
    metadata:
      labels:
        app: freon-user-history-rest
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-user-history-rest
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-Ddapollo.bootstrap.namespaces=user-prop-history.yml,application-commons.yml"
            - "-Dapp_name=freon-user-history-rest"
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          ports:
            - name: rest-port
              containerPort: 8864
          volumeMounts:
            - name: rest-config
              mountPath: /home/<USER>
      volumes:
        - name: rest-config
          configMap:
            name: freon-message-entities-cm
---
apiVersion: v1
kind: Service
metadata:
  name: freon-user-privacy-service
spec:
  selector:
    app: freon-user-privacy-rest
  ports:
    - name: rest-port
      protocol: TCP
      port: 8864
      targetPort: 8864
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-user-privacy-rest
  namespace:
  labels:
    app: freon-user-privacy-rest
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-user-privacy-rest
  template:
    metadata:
      labels:
        app: freon-user-privacy-rest
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-user-privacy-rest
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-Ddapollo.bootstrap.namespaces=user-privacy.yml,application-commons.yml"
            - "-Dapp_name=freon-user-privacy-rest"
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          ports:
            - name: rest-port
              containerPort: 8864
          volumeMounts:
            - name: rest-config
              mountPath: /home/<USER>
      volumes:
        - name: rest-config
          configMap:
            name: freon-message-entities-cm