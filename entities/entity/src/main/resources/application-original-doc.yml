server:
  port: 8592

route:
  rest: false
  path: document

spring:
  application:
    name: freon-message-original-document

antdu:
  metrics:
    clusterName: freon-message-original-document

freon:
  entity:
    classpath: com.antfact.commons.v2.model.StatusPack$Document
    isSendStreamCheck: true #是否发送streamcheck
    checkPropsFieldName: props  #易变属性字段
    ignoreTimeStampSuffix: true #是否忽略时间戳后缀
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: docId,contentId,title,content,trimContent,imageUrl,url,createdAt,displayTime,associateUserId,userId,userHead,userName,nickName,scope,mediaType,link,replyStatusId,replyUserId,replyUserName,retweetId,retweetUserId,retweetUserName,location,ip,ipLocation,source,latitude,longitude,vip,vipType,siteIP,domain,channelName,keywords,imageUrls,groupId,groupName,countryCode,stateType,websiteName,userDescribe,originalId,platform,tagsWithScore,important,originalSeedUrl,influence,originalLocation
    field:
      primaryKey: docId
      createat: createdAt
      source: source
      delCache: important

properties:
  storage:
    buka:
      producerTopic: message-origin-status-topic
    jobs:
      cbase_hot:
        runnable: true
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.OriginalDataSkinnyStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - new_webs
            group: message-original-document-group-cbase-hot-new
            persistId: persistence-message-original-document-cbase-hot
            persistLimitSpeed: 10000
          deflect:
            threadCount: 3
            topic: message-original-document-topic-deflect-cbase-hot
            group: message-original-document-group-deflect-cbase-hot
            persistId: persistence-message-original-document-cbase-hot-deflect
            persistLimitSpeed: 10000
          failback:
            threadCount: 3
            topic: message-original-document-topic-failback-cbase-hot
            group: message-original-document-group-failback-cbase-hot
            persistId: persistence-message-original-document-cbase-hot-failback
            persistLimitSpeed: 10000

cbase:
  keyspace: yuqing_skinny
  table: document
  clusters:
    map:
      hot_cluster:
        enable: true
        clusterNodes: 10.20.2.43,10.20.2.44,10.20.2.45,10.20.2.46,10.20.2.47
        clusterName: yu-hot-cluster
        port: 9042
        datacenter: datacenter1
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 7
        ttlMax: 604800
        ttlMin: 172800
        sequenceNo: 0

cache:
  name_pre: message.original.document.

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: original-document-reader-ratelimiter

persist:
  executor:
    max_thread: 12