/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.disruptor.core;

import com.antdu.freon.disruptor.core.entity.TranslatorDataWapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.SequenceBarrier;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.WorkerPool;
import com.lmax.disruptor.dsl.ProducerType;

import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import lombok.Getter;

/**
 * <p>ringbuffer 工厂类</p>
 * <p/>
 * 创建日期 2019/11/7
 *
 * <AUTHOR> ping<PERSON>(<EMAIL>)
 * @since 1.0.0
 */
public class RingBufferWorkerPoolFactory {

    /**
     * 单例模式
     */
    private static class SingletonHolder {
        /**
         * 单例对象
         */
        static final RingBufferWorkerPoolFactory INSRANCE = new RingBufferWorkerPoolFactory();
    }

    public RingBufferWorkerPoolFactory() {
    }

    /**
     * 获取单例
     *
     * @return
     */
    public static RingBufferWorkerPoolFactory getInstance() {
        return SingletonHolder.INSRANCE;
    }

    /**
     * 生产map
     */
    private static Map<String, MessageProducer> producerMap = new ConcurrentHashMap<>();

    /**
     * 消费map
     */
    private static Map<String, MessageConsumer> consumerMap = new ConcurrentHashMap<>();

    /**
     * 线程池coreThread 与maxThread的阻塞系数
     */
    private static final Integer BLOCK_COEFFICIENT = 5;

    /**
     * ringbuffer容器
     */
    private RingBuffer<TranslatorDataWapper> ringBuffer;

    /**
     * 自定义线程池
     */
    private ExecutorService executorService;

    /**
     * 生产者数量
     */
    @Getter
    private Integer producerSize;

    /**
     * 初始化启动
     *
     * @param type             生产者类型
     * @param bufferSize       buffer大小
     * @param waitStrategy     等待策略
     * @param messageConsumers 消费数组
     * @param coreThread       线程池核心线程数
     * @param keepAliveTime    保持时间
     * @param threadQueueSize  队列大小
     * @param maxRetryTimes    消费失败最大重试次数
     */
    public void initAndStart(ProducerType type, Integer bufferSize, WaitStrategy waitStrategy,
                             MessageConsumer[] messageConsumers, Integer coreThread, Integer keepAliveTime,
                             Integer threadQueueSize, Integer producerSize, int maxRetryTimes) {
        //构建ringBuffer对象
        ringBuffer = RingBuffer.create(type, TranslatorDataWapper::new, bufferSize, waitStrategy);

        //设置序号栅栏
        SequenceBarrier sequenceBarrier = ringBuffer.newBarrier();

        //设置工作池
        WorkerPool<TranslatorDataWapper> workerPool =
            new WorkerPool<>(ringBuffer, sequenceBarrier, new EventExceptionHandler(ringBuffer, maxRetryTimes),
                             messageConsumers);

        //把所构建的消费者置入池中
        Arrays.stream(messageConsumers).forEach(m -> consumerMap.put(m.getConsumerId(), m));

        //添加sequences
        ringBuffer.addGatingSequences(workerPool.getWorkerSequences());

        // 设置生产者数量
        this.producerSize = producerSize;

        //工作池启动
        executorService = new ThreadPoolExecutor(
            coreThread, coreThread * BLOCK_COEFFICIENT, keepAliveTime, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(threadQueueSize),
            new ThreadFactoryBuilder()
                .setDaemon(false).setNameFormat("disruptor-workerPool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());
        workerPool.start(executorService);
    }

    /**
     * 根据id获取生产者
     *
     * @param producerId 生成者id
     * @return
     */
    public MessageProducer getProducer(String producerId) {
        return producerMap.computeIfAbsent(producerId, key -> new MessageProducer(key, ringBuffer));
    }

    /**
     * 获取消费者集合
     *
     * @return 消费者集合
     */
    public Collection<MessageConsumer> getConusmerCollect() {
        return consumerMap.values();
    }

    /**
     * 销毁
     */
    public void destory() {
        producerMap.clear();
        consumerMap.clear();
        executorService.shutdown();
    }
}
