/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.commons.utils;

import com.antdu.freon.commons.exception.core.FreonErrorCodes;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <p>json装换工具类</p>
 * <p/>
 * 创建日期 2018/10/10
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
public class JsonUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonUtil.class);

    /**
     * json对象
     */
    private static final ObjectMapper JSON_OBJECT_MAPPER;

    static {
        SimpleModule simpleModule = new SimpleModule("AntCensor Jackson Module", Version.unknownVersion());

        //忽略未知字段，防止新增字段反序列化异常
        JSON_OBJECT_MAPPER = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        JSON_OBJECT_MAPPER.registerModule(simpleModule);
    }

    /**
     * 序列化对象为 JSON 字符串
     *
     * @param obj {@link Object} Object对象,当转换失败时则返回null
     * @return {@link String} 符合json规范的String串
     */
    public static String toJson(Object obj) {
        try {
            return JSON_OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception ex) {
            LOGGER.error("序列化对象为 JSON 字符串时出现错误！", ex);
            throw FreonErrorCodes.SERIALIZER_ERROR.getException();
        }
    }

    /**
     * 反序列化 JSON 字符串为指定类型的对象
     *
     * @param json  {@link String} 符合json规范的字符串
     * @param clazz {@link Class} 转换的目标类型
     * @return {@link Object} 目标对象,如果转换过程中发生错误则返回null
     * @throws java.util.zip.DataFormatException 当数据格式不正确或者转换过程是发生不可预知错误时抛出此异常
     */
    public static <T> T parseJson(String json, Class<T> clazz) {
        try {
            return JSON_OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception ex) {
            LOGGER.error("反序列化 JSON 字符串为指定类型的对象时出现错误！" + json);
            throw FreonErrorCodes.DESERIALIZER_ERROR.getException();
        }
    }

    /**
     * 将Json格式字符串转换成指定类型的数据对象
     *
     * @param json         {@link String} 符合json规范的字符串
     * @param valueTypeRef {@link TypeReference} 符合json规范的字符串
     * @return {@link Object} 目标对象,如果转换过程中发生错误则返回null
     * @throws java.util.zip.DataFormatException 当数据格式不正确或者转换过程是发生不可预知错误时抛出此异常
     */
    public static <T> T parseJson(String json, TypeReference<T> valueTypeRef) {
        try {
            return JSON_OBJECT_MAPPER.readValue(json, valueTypeRef);
        } catch (Exception e) {
            LOGGER.error("反序列化 JSON 字符串为指定类型的对象时出现错误！" + json);
            throw FreonErrorCodes.DESERIALIZER_ERROR.getException();
        }
    }
}
