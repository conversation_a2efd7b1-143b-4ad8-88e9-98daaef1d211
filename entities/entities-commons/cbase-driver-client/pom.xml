<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
  ~ 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>entities-commons</artifactId>
        <groupId>com.antdu.freon.entities.commons</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>Antdu.com :: Freon Entities CBase Driver Client</name>
    <artifactId>cbase-driver-client</artifactId>

    <properties>
        <cassandra-driver-core.version>4.9.0</cassandra-driver-core.version>
        <jackson.version>2.9.6</jackson.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.datastax.oss</groupId>
            <artifactId>java-driver-core</artifactId>
            <version>${cassandra-driver-core.version}</version>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon</groupId>
            <artifactId>freon-circuitbreaker</artifactId>
            <version>${freon.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon</groupId>
            <artifactId>freon-caster</artifactId>
            <version>${freon.framework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon.entities.commons</groupId>
            <artifactId>commons</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>${caffeine.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.antdu.freon.entities.commons</groupId>
            <artifactId>base-storage</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.antdu.analysis</groupId>
            <artifactId>loading-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.15.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <configuration>
                    <effort>Max</effort>
                    <threshold>Default</threshold>
                    <excludeFilterFile>${project.build.directory}/../findbugs-exclude.xml</excludeFilterFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
