/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.cbase.driverclient.persist.skinny.special.ignore;

import com.antdu.freon.cbase.driverclient.persist.skinny.special.MutabilityPropertiesStorage;
import com.antdu.freon.spi.AcknowledgeHandler;
import com.google.protobuf.GeneratedMessageV3;

import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.stream.Collectors;

/**
 * <p>忽略时间戳的易变属性标签存储</p>
 * <p/>
 * 创建日期 2021/3/10
 *
 * @param <T> 存储实体泛型
 * @param <B> 存储实体builder泛型
 * @param <R> 查询key泛型
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Component
public class IgnoreTimeStampMutapropStorage<T, B extends GeneratedMessageV3.Builder<B>, R>
    extends MutabilityPropertiesStorage<T, B, R> {

    @Override
    protected void skinnyRowPersist(Collection<T> collection, AcknowledgeHandler<T> acknowledgeHandler) {
        collection = collection.stream().map(this::dataProcess).collect(Collectors.toSet());
        super.skinnyRowPersist(collection, acknowledgeHandler);
    }


}
