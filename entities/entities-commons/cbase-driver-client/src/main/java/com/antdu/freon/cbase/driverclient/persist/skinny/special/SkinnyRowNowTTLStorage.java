/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.cbase.driverclient.persist.skinny.special;

import com.antdu.freon.cbase.driverclient.core.CqlSessionWrapper;
import com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage;
import com.google.protobuf.GeneratedMessageV3;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import scala.Function3;

/**
 * <p>按照写入时间来算ttl</p>
 * <p/>
 * 创建日期 2021/12/20
 *
 * @param <T> 存储实体泛型
 * @param <B> 存储实体builder泛型
 * @param <R> 查询key泛型
 * <AUTHOR> @ eefung.com)
 * @since 1.0.0
 */
@Slf4j
@Component
public class SkinnyRowNowTTLStorage<T, B extends GeneratedMessageV3.Builder<B>, R> extends SkinnyRowStorage<T, B, R> {
    @Override
    protected Function3<T, CqlSessionWrapper, Long, Integer> getTTLFunction() {
        return (m, factory, now) -> (int) factory.getTtlMax();
    }
}
