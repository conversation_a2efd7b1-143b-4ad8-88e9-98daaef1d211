FROM openjdk:8-alpine

MAINTAINER liaopingzhu <<EMAIL>>

ENV APP_USER="ant" \
    APP_GROUP="ant" \
    APP_UID="1000"

ENV GOSU_VERSION 1.11

RUN set -eux; \
	\
	apk add --no-cache --virtual .gosu-deps \
		ca-certificates \
		dpkg \
		gnupg \
	; \
	\
	dpkgArch="$(dpkg --print-architecture | awk -F- '{ print $NF }')"; \
	wget -O /usr/local/bin/gosu "http://10.20.1.21/gosu/gosu-$dpkgArch"; \
	wget -O /usr/local/bin/gosu.asc "http://10.20.1.21/gosu/gosu-$dpkgArch.asc"; \
	\
# verify the signature
	export GNUPGHOME="$(mktemp -d)"; \
	command -v gpgconf && gpgconf --kill all || :; \
	rm -rf "$GNUPGHOME" /usr/local/bin/gosu.asc; \
	\
# clean up fetch dependencies
	apk del --no-network .gosu-deps; \
	\
	chmod +x /usr/local/bin/gosu; \
# verify that the binary works
	gosu --version; \
	gosu nobody true

COPY --chown=1000:1000 entrypoint.sh /entrypoint.sh

#需要jstack,jmap等命令做线程/gc分析
RUN apk add tini

RUN chmod 0775 /entrypoint.sh ;\
    mkdir /shared && cd /home;\
    chown -R $APP_UID:$APP_UID /shared/ ;\
    wget http://10.20.1.21/skywalking/apache-skywalking-apm-7.0.0.tar.gz; \
    tar -zxf apache-skywalking-apm-7.0.0.tar.gz; \
    mv apache-skywalking-apm-bin/agent ./; \
    rm apache-skywalking-apm-7.0.0.tar.gz apache-skywalking-apm-bin -rf ;\
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo 'Asia/Shanghai' >/etc/timezone

VOLUME /shared

WORKDIR /home

ENTRYPOINT ["/entrypoint.sh"]